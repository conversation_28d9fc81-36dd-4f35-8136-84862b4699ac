{
  "compilerOptions": {
    "baseUrl": "./src",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "inlineSourceMap": true,
    "module": "esnext",
    "moduleResolution": "node",
    "noFallthroughCasesInSwitch": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "strictPropertyInitialization": false,
    "outDir": "dist",
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "esnext",
    "jsx": "react",
    "declaration": true,
    "composite": true,
    "rootDir": "./src/",
    "paths": {
      "@/*": ["*"]
    }
  },
  "include": ["src/**/*.ts"],
}
