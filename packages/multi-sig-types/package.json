{"name": "@minotaur-ergo/multi-sig-types", "version": "1.0.0", "description": "Multi signature types for minotaur", "repository": "https://github.com/minotaur-ergo/minotaur", "license": "GPL-3.0", "author": "vorujack", "type": "module", "main": "dist/index.js", "scripts": {"prettify": "npx prettier --ignore-unknown --write .", "type-check": "tsc", "build": "tsc && npm run add-icons"}, "files": ["src"], "keywords": [], "dependencies": {"@noble/curves": "^1.9.2", "ergo-lib-wasm-browser": "^0.26.0"}, "devDependencies": {"@types/node": "^20.17.10", "tsx": "^4.19.2"}}