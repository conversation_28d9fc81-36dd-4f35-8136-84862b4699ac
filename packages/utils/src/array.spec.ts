import { describe, it, expect, vi } from 'vitest';
import { createEmptyArray, createEmptyArrayWithIndex, iterateIndexes } from './array';

describe('array utilities', () => {
  describe('createEmptyArray', () => {
    it('should create an array with specified length and default value', () => {
      const result = createEmptyArray(3, 'test');
      expect(result).toEqual(['test', 'test', 'test']);
      expect(result).toHaveLength(3);
    });

    it('should create an empty array when length is 0', () => {
      const result = createEmptyArray(0, 'test');
      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should work with different data types', () => {
      const numberArray = createEmptyArray(2, 42);
      expect(numberArray).toEqual([42, 42]);

      const booleanArray = createEmptyArray(2, true);
      expect(booleanArray).toEqual([true, true]);

      const objectArray = createEmptyArray(2, { id: 1 });
      expect(objectArray).toEqual([{ id: 1 }, { id: 1 }]);
    });

    it('should create array with null values', () => {
      const result = createEmptyArray(2, null);
      expect(result).toEqual([null, null]);
    });

    it('should create array with undefined values', () => {
      const result = createEmptyArray(2, undefined);
      expect(result).toEqual([undefined, undefined]);
    });

    it('should handle large arrays', () => {
      const result = createEmptyArray(1000, 0);
      expect(result).toHaveLength(1000);
      expect(result.every(item => item === 0)).toBe(true);
    });

    it('should create independent references for object values', () => {
      const result = createEmptyArray(2, { value: 1 });
      result[0].value = 2;
      // Note: This test shows that the same object reference is used
      expect(result[1].value).toBe(2);
    });
  });

  describe('createEmptyArrayWithIndex', () => {
    it('should create an array with sequential indexes', () => {
      const result = createEmptyArrayWithIndex(5);
      expect(result).toEqual([0, 1, 2, 3, 4]);
      expect(result).toHaveLength(5);
    });

    it('should create an empty array when length is 0', () => {
      const result = createEmptyArrayWithIndex(0);
      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should create array with single element when length is 1', () => {
      const result = createEmptyArrayWithIndex(1);
      expect(result).toEqual([0]);
      expect(result).toHaveLength(1);
    });

    it('should handle large arrays', () => {
      const result = createEmptyArrayWithIndex(100);
      expect(result).toHaveLength(100);
      expect(result[0]).toBe(0);
      expect(result[99]).toBe(99);
      expect(result[50]).toBe(50);
    });

    it('should create consecutive numbers starting from 0', () => {
      const result = createEmptyArrayWithIndex(10);
      for (let i = 0; i < result.length; i++) {
        expect(result[i]).toBe(i);
      }
    });
  });

  describe('iterateIndexes', () => {
    it('should call callback for each index', () => {
      const callback = vi.fn();
      const iterator = iterateIndexes(3);
      
      iterator.forEach(callback);
      
      expect(callback).toHaveBeenCalledTimes(3);
      expect(callback).toHaveBeenNthCalledWith(1, 0);
      expect(callback).toHaveBeenNthCalledWith(2, 1);
      expect(callback).toHaveBeenNthCalledWith(3, 2);
    });

    it('should not call callback when length is 0', () => {
      const callback = vi.fn();
      const iterator = iterateIndexes(0);
      
      iterator.forEach(callback);
      
      expect(callback).not.toHaveBeenCalled();
    });

    it('should call callback once when length is 1', () => {
      const callback = vi.fn();
      const iterator = iterateIndexes(1);
      
      iterator.forEach(callback);
      
      expect(callback).toHaveBeenCalledTimes(1);
      expect(callback).toHaveBeenCalledWith(0);
    });

    it('should handle large iterations', () => {
      const callback = vi.fn();
      const iterator = iterateIndexes(1000);
      
      iterator.forEach(callback);
      
      expect(callback).toHaveBeenCalledTimes(1000);
      expect(callback).toHaveBeenNthCalledWith(1, 0);
      expect(callback).toHaveBeenNthCalledWith(1000, 999);
    });

    it('should pass correct index values in sequence', () => {
      const receivedIndexes: number[] = [];
      const iterator = iterateIndexes(5);
      
      iterator.forEach((index) => {
        receivedIndexes.push(index);
      });
      
      expect(receivedIndexes).toEqual([0, 1, 2, 3, 4]);
    });

    it('should allow callback to modify external state', () => {
      let sum = 0;
      const iterator = iterateIndexes(5);
      
      iterator.forEach((index) => {
        sum += index;
      });
      
      expect(sum).toBe(10); // 0 + 1 + 2 + 3 + 4 = 10
    });

    it('should return an object with forEach method', () => {
      const iterator = iterateIndexes(3);
      
      expect(iterator).toHaveProperty('forEach');
      expect(typeof iterator.forEach).toBe('function');
    });

    it('should handle callback that throws error', () => {
      const iterator = iterateIndexes(3);
      const errorCallback = vi.fn(() => {
        throw new Error('Test error');
      });
      
      expect(() => {
        iterator.forEach(errorCallback);
      }).toThrow('Test error');
      
      expect(errorCallback).toHaveBeenCalledTimes(1);
    });
  });
});
