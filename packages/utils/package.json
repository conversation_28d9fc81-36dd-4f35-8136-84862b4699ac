{"name": "@minotaur-ergo/utils", "version": "1.0.0", "description": "Utility functions for Minotaur wallet", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/json-bigint": "^1.0.4", "typescript": "^5.0.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4"}, "files": ["dist/**/*"], "dependencies": {"@minotaur-ergo/types": "^1.0.0", "@rosen-clients/ergo-explorer": "^1.1.5", "bip32": "^4.0.0", "blakejs": "^1.2.1", "bs58": "^6.0.0", "crypto-js": "^4.2.0", "ergo-lib-wasm-browser": "0.26.0", "json-bigint": "^1.0.0", "tiny-secp256k1": "2.2.3"}}