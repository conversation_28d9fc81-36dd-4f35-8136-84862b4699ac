import MultiSigKey from '@/entities/MultiSigKey';
import Wallet from '@/entities/Wallet';
import { DataSource, Repository } from 'typeorm';

export class MultiSigDbAction {
  private static instance: MultiSigDbAction;
  private repository: Repository<MultiSigKey>;

  constructor(dataSource: DataSource) {
    this.repository = dataSource.getRepository(MultiSigKey);
  }

  static getInstance = () => {
    if (MultiSigDbAction.instance) {
      return MultiSigDbAction.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    MultiSigDbAction.instance = new MultiSigDbAction(dataSource);
  };

  createKey = async (
    wallet: Wallet,
    key_or_address: string,
    sign_wallet: Wallet | null,
  ) => {
    const entity = {
      wallet: wallet,
      extended_key: key_or_address,
      sign_wallet: sign_wallet,
    };
    return this.repository.insert(entity);
  };

  getWalletInternalKey = async (walletId: number) => {
    const data = await this.repository
      .createQueryBuilder()
      .where('walletId = :walletId', { walletId: walletId })
      .andWhere('signWalletId is not null')
      .getRawOne();
    if (data) {
      return await WalletDbAction.getInstance().getWalletById(
        data.MultiSigKey_signWalletId,
      );
    }
    return undefined;
  };

  getWalletExternalKeys = async (walletId: number) => {
    return await this.repository
      .createQueryBuilder()
      .where('walletId = :walletId', { walletId: walletId })
      .andWhere('signWalletId is null')
      .getMany();
  };

  getWalletKeys = async (walletId: number) => {
    return await this.repository
      .createQueryBuilder()
      .where('walletId = :walletId', { walletId: walletId })
      .getMany();
  };

  deleteWalletKeys = async (walletId: number) => {
    return await this.repository
      .createQueryBuilder()
      .delete()
      .where('walletId = :walletId', { walletId })
      .execute();
  };
}

export class MultiStoreDbAction {
  private inputRepository: Repository<MultiSignInput>;
  private hintRepository: Repository<MultiSigHint>;
  private rowRepository: Repository<MultiSignRow>;
  private txRepository: Repository<MultiSignTx>;
  private dataSource: DataSource;

  private static instance: MultiStoreDbAction;

  constructor(dataSource: DataSource) {
    this.inputRepository = dataSource.getRepository(MultiSignInput);
    this.hintRepository = dataSource.getRepository(MultiSigHint);
    this.rowRepository = dataSource.getRepository(MultiSignRow);
    this.txRepository = dataSource.getRepository(MultiSignTx);
    this.dataSource = dataSource;
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    MultiStoreDbAction.instance = new MultiStoreDbAction(dataSource);
  };

  /**
   * Completely removes a multi-signature transaction row and all its related data.
   *
   * This function performs a cascading delete operation for a multi-signature transaction,
   * removing all associated data in a transactional manner. It deletes all inputs, transaction
   * chunks, hints, and finally the row itself. If any part of the deletion fails,
   * the entire operation is rolled back to maintain database integrity.
   *
   * @param rowId - The ID of the multi-signature row to delete
   */
  public deleteEntireRow = async (rowId: number) => {
    const row = await this.getRowById(rowId);
    if (row) {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        await queryRunner.manager
          .getRepository(MultiSignInput)
          .createQueryBuilder()
          .delete()
          .where({ tx: row })
          .execute();
        await queryRunner.manager
          .getRepository(MultiSignTx)
          .createQueryBuilder()
          .delete()
          .where({ tx: row })
          .execute();
        await queryRunner.manager
          .getRepository(MultiSigHint)
          .createQueryBuilder()
          .delete()
          .where({ tx: row })
          .execute();
        await queryRunner.manager
          .getRepository(MultiSignRow)
          .createQueryBuilder()
          .delete()
          .where({ id: row.id })
          .execute();
        await queryRunner.commitTransaction();
      } catch (e) {
        await queryRunner.rollbackTransaction();
        throw e;
      }
    }
  };

  /**
   * Creates a new multi-signature transaction row in the database.
   *
   * This function inserts a new multi-signature transaction row with the provided details
   * into the database. It acts as the foundation for multi-signature transaction tracking,
   * creating the base record that other transaction components will reference.
   *
   * @param walletId - The ID of the wallet the transaction belongs to
   * @param txId - The transaction ID
   * @returns The newly created multi-signature transaction row
   */
  public insertMultiSigRow = async (walletId: number, txId: string) => {
    const wallet = await WalletDbAction.getInstance().getWalletById(walletId);
    const old = await this.rowRepository
      .createQueryBuilder()
      .select()
      .where('txId=:txId AND walletId=walletId', { txId, walletId: walletId })
      .getOne();
    if (old === null) {
      await this.rowRepository.insert({
        txId: txId,
        wallet: wallet,
      });
    } else {
      await this.rowRepository
        .createQueryBuilder()
        .update()
        .set({ wallet: wallet })
        .where('id=:id', { id: old.id })
        .execute();
    }
    return await this.rowRepository.findOneBy({ txId: txId });
  };

  /**
   * Stores a serialized transaction for a multi-signature row.
   *
   * This function saves the transaction bytes to the database, splitting them
   * into manageable chunks if they exceed the maximum size. It first deletes any
   * existing transaction data for the given row to maintain data consistency.
   *
   * @param row - The multi-signature transaction row to associate with
   * @param txBytes - The serialized transaction bytes as a string
   */
  public insertMultiSigTx = async (row: MultiSignRow, txBytes: string) => {
    await this.txRepository
      .createQueryBuilder()
      .delete()
      .where({ tx: row })
      .execute();
    const txChunks = sliceToChunksString(txBytes, TX_CHUNK_SIZE);
    for (const [index, chunk] of txChunks.entries()) {
      await this.txRepository.insert({
        tx: row,
        bytes: chunk,
        idx: index,
      });
    }
  };

  /**
   * Inserts input box data for a multi-signature transaction.
   *
   * This function stores the serialized input box bytes in the database for a
   * multi-signature transaction. It first removes any existing inputs associated
   * with the provided row to ensure data consistency, then adds each input with
   * its corresponding data.
   *
   * @param row - The multi-signature transaction row to associate these inputs with
   * @param inputs - Array of serialized input box bytes as strings
   */
  public insertMultiSigInputs = async (
    row: MultiSignRow,
    inputs: Array<string>,
  ) => {
    await this.inputRepository
      .createQueryBuilder()
      .delete()
      .where({ tx: row })
      .execute();
    for (const input of inputs) {
      await this.inputRepository.insert({
        tx: row,
        bytes: input,
      });
    }
  };

  /**
   * Stores hint data for a multi-signature transaction.
   *
   * This function saves hint data including commitments, proofs, and secrets for a
   * multi-signature transaction. It first deletes any existing hints associated with
   * the transaction row, then inserts new hints based on the provided data. The function
   * handles both real and simulated hint types, preserving their relationship to
   * specific inputs and signer indices.
   *
   * @param row - The multi-signature transaction row to associate hints with
   * @param commitments - 2D array of hint data organized by [inputIndex][signerIndex]
   */
  public insertMultiSigHints = async (
    row: MultiSignRow,
    commitments: Array<Array<MultiSigDataHint>>,
  ) => {
    await this.hintRepository
      .createQueryBuilder()
      .delete()
      .where({ tx: row })
      .execute();
    for (const [inputIndex, inputHint] of commitments.entries()) {
      for (const [index, hint] of inputHint.entries()) {
        if (hint.Commit) {
          await this.hintRepository.insert({
            type:
              hint.Type === MultiSigDataHintType.REAL
                ? MultiSigHintType.Real
                : MultiSigHintType.Simulated,
            idx: index,
            inpIdx: inputIndex,
            commit: hint.Commit,
            proof: hint.Proof,
            tx: row,
            secret: hint.Secret,
          });
        }
      }
    }
  };

  /**
   * Retrieves a specific multi-signature transaction row by its ID.
   *
   * This function performs a direct database lookup to find a multi-signature
   * transaction row with the specified ID. It's commonly used when accessing
   * a known transaction for viewing details or performing operations.
   *
   * @param id - The unique identifier of the multi-signature row to retrieve
   * @returns The found multi-signature row or null if not found
   */
  public getRowById = async (id: number) => {
    return await this.rowRepository.findOneBy({ id });
  };

  /**
   * Retrieves all multi-signature transaction rows associated with a specific wallet.
   *
   * This function queries the database to find all multi-signature transaction rows
   * that belong to the specified wallet ID. Optionally filters results by a list of
   * transaction IDs if provided.
   *
   * @param walletId - The ID of the wallet to get transactions for
   * @param txIds - Optional array of transaction IDs to filter results
   * @returns An array of multi-signature transaction rows
   */
  public getWalletRows = (walletId: number, txIds?: Array<string>) => {
    let query = this.rowRepository
      .createQueryBuilder()
      .select()
      .where('walletId=:walletId', { walletId });
    if (txIds) {
      query = query.andWhere('txId IN (:...txIds)', { txIds });
    }
    return query.getMany();
  };

  /**
   * Retrieves the complete transaction bytes for a multi-signature transaction.
   *
   * This function fetches all transaction chunks from the database for a specific
   * transaction row, orders them by index, and concatenates them to rebuild the
   * complete transaction data. The explicit sort ensures consistent ordering even
   * when the database query order might vary.
   *
   * @param row - The multi-signature transaction row
   * @returns The complete serialized transaction as a string
   */
  public getTx = async (row: MultiSignRow) => {
    const elements = await this.txRepository
      .createQueryBuilder()
      .select()
      .where({ tx: row })
      .orderBy('idx', 'ASC')
      .getMany();
    const sortedElements = elements.sort((a, b) => a.idx - b.idx);
    return sortedElements.map((item) => item.bytes).join('');
  };

  /**
   * Retrieves the input box bytes for a multi-signature transaction.
   *
   * This function fetches all input box bytes stored in the database for a specific
   * multi-signature transaction row. The results are ordered by ID to ensure consistent
   * retrieval order across multiple calls.
   *
   * @param row - The multi-signature transaction row
   * @returns Array of serialized input box bytes as strings
   */
  public getInputs = async (row: MultiSignRow) => {
    const elements = await this.inputRepository
      .createQueryBuilder()
      .select()
      .where({ tx: row })
      .orderBy('id', 'ASC')
      .getMany();
    return elements.map((item) => item.bytes);
  };

  /**
   * Retrieves hints for a multi-signature transaction.
   *
   * This function fetches hint data and their associated secrets from the database
   * for a specific multi-signature transaction and transforms them into an appropriate
   * data structure for use in the application. For non-existent data, empty arrays
   * with the correct dimensions are created.
   *
   * @param row - The multi-signature transaction row
   * @param inputCount - Number of inputs in the transaction
   * @param signerCount - Number of possible signers for each input
   * @returns Object containing hints and secrets as 2D arrays
   */
  public getHints = async (
    row: MultiSignRow,
    inputCount: number,
    signerCount: number,
  ) => {
    const hintEntities = await this.hintRepository.find({
      where: {
        tx: {
          id: row.id,
        },
      },
    });
    return createEmptyArray(inputCount, '').map((_, inputIndex) => {
      return createEmptyArray(signerCount, '').map((_, signerIndex) => {
        const filtered = hintEntities.find(
          (item) => item.idx === signerIndex && item.inpIdx === inputIndex,
        );
        if (filtered) {
          return new MultiSigDataHint(
            inputIndex,
            signerIndex,
            Buffer.from(filtered.commit, 'hex'),
            Buffer.from(filtered.proof || '', 'hex'),
            Buffer.from(filtered.secret || '', 'hex'),
            filtered.type === MultiSigHintType.Real
              ? MultiSigDataHintType.REAL
              : MultiSigDataHintType.SIMULATED,
          );
        } else {
          return new MultiSigDataHint(
            inputIndex,
            signerIndex,
            undefined,
            undefined,
            undefined,
            MultiSigDataHintType.REAL,
          );
        }
      });
    });
  };
}
