import Asset from '@/entities/Asset';
import { TokenInfo } from '@/types';
import { DataSource, Repository } from 'typeorm';

export class AssetDbAction {
  private assetRepository: Repository<Asset>;
  private static instance: AssetDbAction;

  private constructor(dataSource: DataSource) {
    this.assetRepository = dataSource.getRepository(Asset);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    this.instance = new AssetDbAction(dataSource);
  };

  getAssetByAssetId = async (
    assetId: string,
    network_type: string,
  ): Promise<Asset | null> => {
    return await this.assetRepository.findOneBy({
      asset_id: assetId,
      network_type: network_type,
    });
  };

  createOrUpdateAsset = async (info: TokenInfo, networkType: string) => {
    const dbEntity = await this.getAssetByAssetId(info.id, networkType);
    const entity = {
      asset_id: info.id,
      box_id: info.boxId,
      tx_id: info.txId ?? undefined,
      emission_amount: info.emissionAmount?.toString() ?? undefined,
      height: info.height,
      name: info.name,
      network_type: networkType,
      description: info.description,
      decimal: info.decimals,
    };
    if (dbEntity) {
      await this.assetRepository
        .createQueryBuilder()
        .update()
        .set(entity)
        .where('id=:id', { id: dbEntity.id })
        .execute();
    } else {
      await this.assetRepository.insert(entity);
    }
    return await this.getAssetByAssetId(info.id, networkType);
  };

  getAllAsset = async (network_type: string) => {
    return await this.assetRepository.findBy({ network_type: network_type });
  };

  getUnFetchedAssets = async (networkType: string) => {
    return this.assetRepository
      .createQueryBuilder()
      .select()
      .where("(tx_id is NULL OR tx_id ='') AND network_type=:networkType", {
        networkType,
      })
      .getMany();
  };

  getUnConfirmedAssets = async (networkType: string, height: number) => {
    return this.assetRepository
      .createQueryBuilder()
      .select()
      .where(
        "tx_id is not NULL AND tx_id <> '' AND network_type=:networkType AND height > :height",
        {
          networkType,
          height,
        },
      )
      .getMany();
  };
}
