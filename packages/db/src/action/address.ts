import Address from '@/entities/Address';
import Wallet from '@/entities/Wallet';
import { DataSource, Repository } from 'typeorm';

export class AddressDbAction {
  private repository: Repository<Address>;
  private static instance: AddressDbAction;

  private constructor(
    dataSource: DataSource,
    private getWalletById: (walletId: number) => Promise<Wallet|null>,
  ) {
    this.repository = dataSource.getRepository(Address);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (
    dataSource: DataSource,
    getWalletById: (walletId: number) => Promise<Wallet|null>,
  ) => {
    AddressDbAction.instance = new AddressDbAction(dataSource, getWalletById);
  };

  getLastAddressIndex = async (wallet_id: number): Promise<number> => {
    const queryBuilder = this.repository.createQueryBuilder('lastIndex');
    queryBuilder
      .select('MAX("idx")', 'lastIndex')
      .where(`walletId=${wallet_id}`);
    const res = await queryBuilder.getRawOne();
    return res === undefined ||
    res.lastIndex === undefined ||
    res.lastIndex === null
      ? -1
      : res.lastIndex;
  };

  getAllAddresses = async (): Promise<Array<Address>> => {
    return await this.repository.find({
      relations: ['wallet'],
    });
  };

  getWalletAddresses = async (walletId: number) => {
    return this.repository
      .createQueryBuilder()
      .where('walletId=:walletId', { walletId })
      .getMany();
  };

  getAddressById = async (addressId: number) => {
    return await this.repository.findBy({ id: addressId });
  };

  getAddressByAddressString = async (address: string) => {
    return await this.repository.findOneBy({ address: address });
  };

  saveAddress = async (
    walletId: number,
    name: string,
    address: string,
    path: string,
    index: number,
  ) => {
    const wallet = await WalletDbAction.getInstance().getWalletById(walletId);
    if (!wallet) throw Error('invalid wallet id');
    const entity = {
      name: name,
      address: address,
      path: path,
      idx: index,
      network_type: wallet.network_type,
      wallet: wallet,
    };
    return await this.repository.save(entity);
  };

  updateAddressName = async (addressId: number, newName: string) => {
    return await this.repository
      .createQueryBuilder()
      .update()
      .set({ name: newName })
      .where('id=:id', { id: addressId })
      .execute();
  };

  updateAddressHeight = async (addressId: number, newHeight: number) => {
    return await this.repository
      .createQueryBuilder()
      .update()
      .set({ process_height: newHeight })
      .where('id=:id', { id: addressId })
      .execute();
  };

  deleteWalletAddresses = async (walletId: number) => {
    const addresses = await this.repository
      .createQueryBuilder()
      .select()
      .where('walletId = :walletId', { walletId })
      .getMany();
    if (addresses.length > 0) {
      for (const address of addresses) {
        await BoxDbAction.getInstance().deleteBoxForAddress(address.id);
      }
      await this.repository
        .createQueryBuilder()
        .delete()
        .where('walletId = :walletId', { walletId })
        .execute();
    }
  };
}
