import SavedAddress from '@/entities/SavedAddress';
import { DataSource, Repository } from 'typeorm';

export class SavedAddressDbAction {
  private repository: Repository<SavedAddress>;
  private static instance: SavedAddressDbAction;

  constructor(dataSource: DataSource) {
    this.repository = dataSource.getRepository(SavedAddress);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    SavedAddressDbAction.instance = new SavedAddressDbAction(dataSource);
  };

  saveNewEntity = async (name: string, address: string) => {
    const saved = await this.repository.findBy({ address: address });
    if (saved.length > 0) {
      throw Error('address already exists');
    } else {
      await this.repository.insert({
        address: address,
        name: name,
      });
    }
  };

  updateEntity = async (id: number, name: string, address?: string) => {
    const saved = await this.repository.findBy({ id: id });
    if (saved) {
      if (address && saved[0].address !== address) {
        const addressExists =
          (await this.repository.findBy({ address: address })).length > 0;
        if (addressExists) {
          throw Error('Address already exists');
        }
      }
      await this.repository
        .createQueryBuilder()
        .update()
        .set({ address: address, name: name })
        .where('id=:id', { id: id })
        .execute();
    }
  };

  deleteEntity = async (id: number) => {
    return await this.repository
      .createQueryBuilder()
      .delete()
      .where('id=:id', { id: id })
      .execute();
  };

  getAllAddresses = async () => {
    return this.repository.find();
  };

  getAddressName = async (address: string) => {
    const elements = await this.repository.findBy({ address: address });
    if (elements.length > 0) {
      return elements[0].name;
    }
    return undefined;
  };
}
