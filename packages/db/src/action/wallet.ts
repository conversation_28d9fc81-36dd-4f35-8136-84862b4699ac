import { ConfigType } from '@/entities/Config';
import Wallet, { WalletType } from '@/entities/Wallet';
import { DEFAULT_ADDRESS_PREFIX } from '@/const';
import { DataSource, Repository } from 'typeorm';

export class WalletDbAction {
  private walletRepository: Repository<Wallet>;
  private static instance: WalletDbAction;

  private constructor(
    dataSource: DataSource,
    private invalidate: () => unknown,
    private deleteWalletAddresses: (walletId: number) => Promise<unknown>,
    private deleteMultiSigKeys: (walletId: number) => Promise<unknown>,
    private deleteConfig: (key: string, pinType: string) => Promise<unknown>,
  ) {
    this.walletRepository = dataSource.getRepository(Wallet);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (
    dataSource: DataSource,
    invalidate: () => unknown,
    deleteWalletAddresses: (walletId: number) => Promise<unknown>,
    deleteMultiSigKeys: (walletId: number) => Promise<unknown>,
    deleteConfig: (key: string, pinType: string) => Promise<unknown>,
  ) => {
    WalletDbAction.instance = new WalletDbAction(dataSource, invalidate, deleteWalletAddresses, deleteMultiSigKeys, deleteConfig);
  };

  getWallets = async () => {
    return await this.walletRepository.find();
  };

  getWalletById = async (walletId: number) => {
    return this.walletRepository.findOneBy({ id: walletId });
  };

  changeSeedAndMnemonic = async (
    walletId: number,
    seed: string,
    encrypted_mnemonic: string,
  ) => {
    await this.walletRepository
      .createQueryBuilder()
      .update()
      .set({ seed, encrypted_mnemonic })
      .where('id=:id', { id: walletId })
      .execute();
  };

  createWallet = async (
    name: string,
    type: WalletType,
    seed: string,
    extended_public_key: string,
    network_type: string,
    requiredSign: number,
    encryptedMnemonic: string,
  ) => {
    const wallet = {
      name: name,
      type: type,
      seed: seed,
      extended_public_key: extended_public_key,
      network_type: network_type,
      required_sign: requiredSign,
      encrypted_mnemonic: encryptedMnemonic,
    };
    await this.walletRepository.save(wallet);
    const res = await this.walletRepository.findOneBy({
      name: name,
      type: type,
      seed: seed,
      extended_public_key: extended_public_key,
      network_type: network_type,
      required_sign: requiredSign,
      encrypted_mnemonic: encryptedMnemonic,
    });
    if (!res) throw Error('Can not store wallet');
    return res;
  };

  setWalletName = async (walletId: number, newName: string) => {
    await this.walletRepository
      .createQueryBuilder()
      .update()
      .set({ name: newName })
      .where('id=:id', { id: walletId })
      .execute();
    this.invalidate()
  };

  deleteWallet = async (walletId: number, pinType: string) => {
    await this.deleteWalletAddresses(walletId)
    const entity = await this.getWalletById(walletId);
    if (entity) {
      if (entity.type === WalletType.MultiSig) {
        await this.deleteMultiSigKeys(entity.id);
      }
      await this.walletRepository
        .createQueryBuilder()
        .delete()
        .where('id=:id', { id: walletId })
        .execute();
      await this.deleteConfig(ConfigType.ActiveWallet, pinType);
      this.invalidate()
    }
  };

  protected setFlags = async (walletId: number, flags: Array<string>) => {
    const flagStr = [
      ...new Set(flags.filter(Boolean).map((item) => item.trim())),
    ].join('|');
    await this.walletRepository
      .createQueryBuilder()
      .update()
      .set({ flags: flagStr })
      .where('id=:id', { id: walletId })
      .execute();
  };

  setFlagOnWallet = async (
    walletId: number,
    flag: string,
    remove: boolean = false,
  ) => {
    const wallet = await this.getWalletById(walletId);
    if (wallet) {
      const flags = [...wallet.flags.split('|'), flag].filter(
        (item) => !remove || item !== flag,
      );
      await this.setFlags(walletId, flags);
      this.invalidate()
    }
  };

  setDefaultAddress = async (walletId: number, index: number) => {
    const wallet = await this.getWalletById(walletId);
    if (wallet) {
      const flags = [
        ...wallet.flags
          .split('|')
          .filter((item) => !item.startsWith(DEFAULT_ADDRESS_PREFIX)),
        DEFAULT_ADDRESS_PREFIX + index,
      ];
      await this.setFlags(walletId, flags);
      this.invalidate()
    }
  };
}
