import Config from '@/entities/Config';
import { DataSource, Like, Repository } from 'typeorm';

export class ConfigDbAction {
  private repository: Repository<Config>;
  private static instance: ConfigDbAction;

  private constructor(dataSource: DataSource) {
    this.repository = dataSource.getRepository(Config);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    ConfigDbAction.instance = new ConfigDbAction(dataSource);
  };

  getAllConfig = async (pinType: string) => {
    return await this.repository.find({ where: { pinType: pinType } });
  };

  setConfig = async (key: string, value: string, pinType: string) => {
    const entity = await this.repository.findOneBy({
      key: key,
      pinType: pinType,
    });
    if (entity) {
      return await this.repository
        .createQueryBuilder()
        .update()
        .set({ value: value })
        .where('key=:key AND pinType=:pinType', { key: key, pinType: pinType })
        .execute();
    } else {
      return await this.repository.insert({ key, value, pinType });
    }
  };

  deleteConfig = async (key: string, pinType: string) => {
    const configs = await this.repository.find({
      where: {
        key: key,
        pinType: Like(pinType),
      },
    });
    return this.repository.delete(configs.map((item) => item.id));
  };
}
