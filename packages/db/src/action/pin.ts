import Pin from '@/entities/Pin';
import { DataSource, Repository } from 'typeorm';

export class PinDbAction {
  private repository: Repository<Pin>;
  private static instance: PinDbAction;

  private constructor(dataSource: DataSource) {
    this.repository = dataSource.getRepository(Pin);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    this.instance = new PinDbAction(dataSource);
  };

  getAllPins = async () => {
    return this.repository.find();
  };

  getPinOfType = (type: string) => {
    return this.repository.findOne({
      where: {
        type,
      },
    });
  };

  findPinByValue = (pin: string) => {
    return this.repository.findOne({
      where: {
        value: pin,
      },
      order: {
        type: 'asc',
      },
    });
  };

  deletePinType = async (pinType: string) => {
    await this.repository.delete({
      type: Like(pinType + '%'),
    });
  };

  setPin = async (pin: string, type: string) => {
    const oldPin = await this.getPinOfType(type);
    if (oldPin) {
      await this.repository.update(
        {
          id: oldPin.id,
        },
        {
          value: pin,
        },
      );
    } else {
      await this.repository.insert({
        type,
        value: pin,
      });
    }
  };
}
