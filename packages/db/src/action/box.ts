import Address from '@/entities/Address';
import Box from '@/entities/Box';
import { SpendDetail } from 'minotaur-wallet/src/types/network';
import { DataSource, Repository } from 'typeorm';

export class BoxDbAction {
  private repository: Repository<Box>;
  private static instance: BoxDbAction;

  private constructor(dataSource: DataSource) {
    this.repository = dataSource.getRepository(Box);
  }

  static getInstance = () => {
    if (this.instance) {
      return this.instance;
    }
    throw Error('Not initialized');
  };

  static initialize = (dataSource: DataSource) => {
    BoxDbAction.instance = new BoxDbAction(dataSource);
  };

  spendBox = (boxId: string, spend: SpendDetail) => {
    return this.repository
      .createQueryBuilder()
      .update()
      .set({
        spend_tx_id: spend.tx,
        spend_timestamp: spend.timestamp,
        spend_index: spend.index,
        spend_height: spend.height,
      })
      .where('box_id = :boxId', { boxId })
      .execute();
  };
  deleteBoxByBoxId = (id: number) => {
    return this.repository
      .createQueryBuilder()
      .delete()
      .where('id = :id', { id })
      .execute();
  };

  forkTx = async (txId: string) => {
    await this.repository
      .createQueryBuilder()
      .delete()
      .where('tx_id=:txId', { txId })
      .execute();
    await this.repository
      .createQueryBuilder()
      .update()
      .set({
        spend_height: 0,
        spend_index: 0,
        spend_timestamp: 0,
      })
      .where('spend_tx_id=:txId', { txId })
      .execute();
  };
  getTxBoxes = (
    txId: string,
    addressIds: Array<number>,
  ): Promise<Array<Box>> => {
    return this.repository
      .createQueryBuilder()
      .where(
        'addressId IN (:...addressIds) AND (tx_id = :txId OR spend_tx_id = :txId)',
        { txId, addressIds },
      )
      .getMany();
  };

  getWalletSortedTxIds = (
    walletId: number,
    offset?: number,
    limit?: number,
  ): Promise<Array<{ txId: string; height: number }>> => {
    const createTxs = this.repository
      .createQueryBuilder()
      .select('tx_id', 'txId')
      .addSelect('create_height', 'height')
      .innerJoin('address', 'Address', 'Address.id == box.addressId')
      .where(`address.walletId = ${walletId}`)
      .distinct()
      .getQuery();
    const spendTxs = this.repository
      .createQueryBuilder()
      .select('spend_tx_id', 'txId')
      .addSelect('spend_height', 'height')
      .where('spend_tx_id <> NULL')
      .innerJoin('address', 'Address', 'Address.id == box.addressId')
      .where(`address.walletId = ${walletId}`)
      .andWhere('spend_tx_id IS NOT NULL')
      .distinct()
      .getQuery();
    let query = `SELECT distinct txId, height
                 FROM (${createTxs} UNION ${spendTxs})
                 ORDER BY height DESC`;
    if (limit !== undefined) {
      query += ` LIMIT ${limit}`;
    }
    if (offset !== undefined) {
      query += ` OFFSET ${offset}`;
    }
    return this.repository.query(query);
  };

  getAddressSortedTxIds = (
    addressId: number,
    fromHeight: number,
  ): Promise<Array<{ txId: string; height: number }>> => {
    const createTxs = this.repository
      .createQueryBuilder()
      .select('tx_id', 'txId')
      .addSelect('create_height', 'height')
      .where(`box.addressId = ${addressId}`)
      .andWhere(`box.create_height >= ${fromHeight}`)
      .distinct()
      .getQuery();
    const spendTxs = this.repository
      .createQueryBuilder()
      .select('spend_tx_id', 'txId')
      .addSelect('spend_height', 'height')
      .where('spend_tx_id <> NULL')
      .where(`box.addressId = ${addressId}`)
      .andWhere(`box.spend_height >= ${fromHeight}`)
      .andWhere('spend_tx_id <> NULL')
      .distinct()
      .getQuery();
    const query = `SELECT distinct txId, height
                   FROM (${createTxs} UNION ${spendTxs})
                   ORDER BY height DESC`;
    return this.repository.query(query);
  };

  getAddressBoxes = (
    addresses: Array<number>,
    order = 'create_height',
    direction: 'ASC' | 'DESC' = 'ASC',
    boxIds: Array<string> = [],
  ) => {
    const query = this.repository
      .createQueryBuilder()
      .select()
      .where('addressId IN (:...addressIds)', { addressIds: addresses });
    if (boxIds?.length > 0) {
      query.andWhere('box_id IN (:...boxIds)', { boxIds });
    }
    return query.orderBy(order, direction).getMany();
  };

  getAllBoxById = async (boxId: string): Promise<Array<Box>> => {
    return this.repository.find({
      relations: ['address.wallet'],
      where: {
        box_id: boxId,
      },
    });
  };

  getBoxByBoxId = async (
    boxId: string,
    address: Address,
  ): Promise<Box | null> => {
    return this.repository.findOne({
      where: {
        box_id: boxId,
        address: {
          id: address.id,
        },
      },
    });
  };

  insertOrUpdateBox = async (box: BoxInfo, address: Address) => {
    const dbEntity = await this.getBoxByBoxId(box.boxId, address);
    const entity = {
      box_id: box.boxId,
      tx_id: box.create.tx,
      create_index: box.create.index,
      create_height: box.create.height,
      create_timestamp: box.create.timestamp,
      address: address,
      serialized: box.serialized,
      spend_tx_id: box.spend ? box.spend.tx : null,
      spend_height: box.spend ? box.spend.height : 0,
      spend_timestamp: box.spend ? box.spend.timestamp : 0,
      spend_index: box.spend ? box.spend.index : 0,
    };
    if (dbEntity) {
      await this.repository
        .createQueryBuilder()
        .update()
        .set(entity)
        .where('id=:id', { id: dbEntity.id })
        .execute();
    } else {
      await this.repository.insert(entity);
    }
    return await this.getBoxByBoxId(box.boxId, address);
  };

  getAddressUnspentBoxes = async (
    addresses: Array<number>,
    order = 'create_height',
    direction: 'ASC' | 'DESC' = 'ASC',
  ) => {
    return this.repository
      .createQueryBuilder()
      .select()
      .where('addressId IN (:...addressIds) AND spend_tx_id is NULL', {
        addressIds: addresses,
      })
      .orderBy(order, direction)
      .getMany();
  };

  updateBoxDetailForTx = async (txId: string, info: TxInfo) => {
    await this.repository
      .createQueryBuilder()
      .update()
      .set({
        create_timestamp: info.timestamp,
        create_height: info.height,
      })
      .where('tx_id=:txId', { txId })
      .execute();
    await this.repository
      .createQueryBuilder()
      .update()
      .set({
        spend_height: info.height,
        spend_timestamp: info.timestamp,
      })
      .where('spend_tx_id=:txId', { txId })
      .execute();
  };

  deleteBoxForAddress = async (addressId: number) => {
    return this.repository
      .createQueryBuilder()
      .delete()
      .where('addressId = :addressId', { addressId })
      .execute();
  };
}
