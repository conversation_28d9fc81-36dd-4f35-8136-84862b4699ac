import { AddressDbAction } from '@/action/address';
import { AddressValueDbAction } from '@/action/address-value';
import { AssetDbAction } from '@/action/asset';
import { BoxDbAction } from '@/action/box';
import { ConfigDbAction } from '@/action/config';
import { MultiSigDbAction, MultiStoreDbAction } from '@/action/multi-sig';
import { PinDbAction } from '@/action/pin';
import { SavedAddressDbAction } from '@/action/saved-address';
import { WalletDbAction } from '@/action/wallet';
import { DataSource } from 'typeorm';

const initializeAction = (dataSource: DataSource, invalidate: () => unknown) => {
  WalletDbAction.initialize(dataSource);
  AddressDbAction.initialize(dataSource);
  AddressValueDbAction.initialize(dataSource);
  BoxDbAction.initialize(dataSource);
  AssetDbAction.initialize(dataSource);
  ConfigDbAction.initialize(dataSource);
  PinDbAction.initialize(dataSource);
  SavedAddressDbAction.initialize(dataSource);
  MultiSigDbAction.initialize(dataSource);
  MultiStoreDbAction.initialize(dataSource);
};


export default initializeAction;