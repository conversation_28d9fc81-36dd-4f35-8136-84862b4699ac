{"name": "@minotaur-ergo/db", "version": "1.0.0", "description": "Database manager for minotaur", "repository": "https://github.com/minotaur-ergo/minotaur", "license": "GPL-3.0", "author": "vorujack", "type": "module", "main": "dist/index.js", "scripts": {"prettify": "npx prettier --ignore-unknown --write .", "build": "tsc && npm run add-icons"}, "files": ["src"], "keywords": [], "dependencies": {}, "devDependencies": {"@types/node": "^20.17.10", "tsx": "^4.19.2"}}