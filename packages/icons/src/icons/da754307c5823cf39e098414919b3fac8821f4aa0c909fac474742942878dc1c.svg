<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 2048 2048" width="586" height="586" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(1323,273)" d="m0 0 6 1 12 8 16 12 11 9 15 14 11 11 7 8 13 16 9 13 13 21 10 19 11 27 9 31 5 25 3 31v35l-3 31-6 35-2 2-10-8-16-10-23-11-19-7-27-7-28-5-19-2-17-1h-50l-36 3-41 6-30 6-9 2 17 3 37 7 36 9 45 14 21 8 27 11 20 9 16 8 21 11 24 14 11 7 30 20 21 16 13 10 13 11 10 9 11 9 37 37 9 11 11 12 9 11 13 17 14 19 10 15 15 24 12 21 13 25 7 14 11 25 10 26 10 29 9 30 7 30 5 28 4 30 2 26v56l-2 24-4 29-4 22-7 29-9 29-13 34-14 30-11 21-12 20-9 14-13 18-8 11-13 16-11 13-11 12-9 9-7 8-12 11-13 12-11 9-14 11-16 12-27 18-24 14-16 9-23 11-24 11-30 12-23 8-27 9-40 11-34 8-37 7-40 6-38 4-27 2-21 1h-62l-37-2-39-4-29-4-34-6-40-9-36-10-36-12-31-12-21-9-30-14-32-17-25-15-11-7-24-16-17-13-16-12-14-12-11-9-15-14-12-11-25-25-7-8-13-14-11-14-9-11-13-18-10-15-15-25-15-29-12-27-7-18-11-34-10-40-6-33-5-40-2-28v-76l2-30 4-40 7-47 11-55 12-49 14-49 14-44 16-45 17-44 12-29 12-28 19-42 17-35 11-23 12-23 17-33 13-23 13-24 14-24 17-29 16-26 12-19 16-25 17-25 7-9 3-1 12-1h27l26 3 18 4 17 5 21 9 14 8 13 9 11 9 18 18 12 16 8 13 12 23 10 27 7 26 5 28 4 36-1 4-41 14-33 13-25 11-23 11-22 12-21 12-20 13-17 12-17 13-14 12-13 12-16 16-7 8-10 12-12 17-8 13-12 23-7 17 1 1 10-9 11-9 14-11 21-16 39-26 23-14 23-13 33-17 30-14 37-15 41-14 48-13 34-7 34-5 27-3 42-2 13-1 4-5 16-24 11-16 12-17 13-18 16-21 12-16 13-16 9-11 8-10 10-11 9-11 14-15 7-8 11-12 34-34 8-7 11-10 11-9 16-13 18-13z" fill="#7DC5DE"/>
<path transform="translate(1138,665)" d="m0 0h7l19 5 27 8 24 8 28 11 11 4 11 5 21 9 16 8 23 12 23 13 15 9 7 5 3 1v2l5 2 12 8 5 3v2l4 1 8 6 2 1v2l5 2 14 11 13 11 10 8v2l4 2 29 29v2h2l7 8v2l3 1 18 22v2h2l14 20 10 15 5 7 6 11 8 14 8 16 8 18 10 22 2 10h9l13 4 6 6 3 7 2 6v8l-4 10-7 11-4 5-1 9 2 17 4 37 2 50-1 40-2 18-3 30-2 10-5 28-7 30-7 24-3 9-9 24-7 14-9 19-14 24-8 12-14 19-5 7h-2l-2 4-7 8-9 10-6 7h-2v2l-5 5h-2v2l-8 7-9 8h-2v2l-17 13-11 8-10 7-20 12-9 5-7 4-10 5-15 7-17 7-10 4-21 7-33 8-16 3-23 4-16 2-26 2h-34l-36-2-25-3-14-2-39-9-21-6-12-5-13-5-7-4-10-4-12-7-8-4-9-6-14-10-16-15-11-13-4-6-12-21-5-13-4-16-1-12v-15l3-28 5-21 5-13 8-16 9-13 11-12 10-9 13-9 15-8 19-7 28-8 16-4 11-2h10l2 2h3l1-3 7-1 46-1 84-4h10v-15l-1-13-6-12-9-10-15-13-14-11-8-8-6-12-1-3v-17l4-11 6-9 11-8 6-2 15-1h87l15 1 10 4 8 7 6 8 4 11v18l-3 9-6 10-11 9-19 14-9 9-8 13-4 13-1 15 6-3 23-5 33-11 25-12 15-10 9-8 8-7 2-3h2l2-4 8-10 8-13 7-14 7-18 7-25 2-13-1-2-25-7-20-8-25-13-17-12-8-9-3-7v-13l5-10 7-7 8-4h13l12 5 15 8 25 10 18 5-1-4-4-46-6-41-7-33-9-32-4-12-8-20-8-17-13-23-6-9-5-7v-2h-2l-4-7-14-16-9-10v-2l-4-2v-2l-3-1v-2l-4-2v-2l-4-2-8-8-2-1v-2l-4-2-10-9-17-13-12-9-2-1v-2l-5-2-5-3v-2l-4-1-28-18-15-9-2-1v-2l-6-2-16-10-12-6-23-13-8-6-2-5 2-9z" fill="#74B8CD"/>
<path transform="translate(1138,665)" d="m0 0h7l19 5 27 8 24 8 28 11 11 4 11 5 21 9 16 8 23 12 23 13 15 9 7 5 3 1v2l5 2 12 8 5 3v2l4 1 8 6 2 1v2l5 2 14 11 13 11 10 8v2l4 2 29 29v2h2l7 8v2l3 1 18 22v2h2l14 20 10 15 5 7 6 11 8 14 8 16 8 18 10 22 2 10h9l13 4 6 6 3 7 2 6v8l-4 10-7 11-11 13-9 10-15 15-11 9-19 13-16 9-16 7-19 6-19 3h-33l-25-5-23-7-18-8-21-11-17-12-8-9-3-7v-13l5-10 7-7 8-4h13l12 5 15 8 25 10 18 5-1-4-4-46-6-41-7-33-9-32-4-12-8-20-8-17-13-23-6-9-5-7v-2h-2l-4-7-14-16-9-10v-2l-4-2v-2l-3-1v-2l-4-2v-2l-4-2-8-8-2-1v-2l-4-2-10-9-17-13-12-9-2-1v-2l-5-2-5-3v-2l-4-1-28-18-15-9-2-1v-2l-6-2-16-10-12-6-23-13-8-6-2-5 2-9z" fill="#74B8CD"/>
<path transform="translate(1192,1176)" d="m0 0h87l15 1 10 4 8 7 6 8 4 11v18l-3 9-6 10-11 9-19 14-9 9-8 13-4 13-2 17-1 32v32l9 3 6 1h15l20-4 16-6 12-7 9-8 6-9 4-11 3-14 5-8 9-8 6-2h14l9 4 7 6 6 12v15l-5 16-8 16-9 13-14 14-14 10-16 8-21 7-20 4-8 1h-21l-17-3-26-10h-9l-27 9-17 4-16 2h-21l-21-3-18-5-18-8-15-8-15-11-14-12-7-7-6-9-2-6v-12l5-12 8-8 8-4 4-1h10l9 3 9 7 7 7 7 8 9 8 15 10 11 5 14 4 10 2h25l21-4 11-4v-17l-2-58-3-13-7-12-10-10-11-9-17-14-8-9-5-12v-17l4-11 6-9 11-8 6-2z" fill="#1C2B31"/>
<path transform="translate(740,1075)" d="m0 0 10 1 14 7 10 9 13 10 18 11 19 9 18 6 15 3 8 1h36l27-4 28-7 20-7 16-7 20-11 15-9 3-1h11l10 5 7 8 3 9v7l-4 10-9 10-15 12-19 12-25 13-20 8-25 7-25 5-18 2h-32l-21-3-20-5-17-6-19-9-19-12-14-11-15-14-11-12-9-13-2-4-1-10 3-9 6-7 5-3z" fill="#1C2B31"/>
<path transform="translate(1593,1027)" d="m0 0 10 1 8 5 6 7 3 8v8l-4 10-7 11-11 13-9 10-15 15-11 9-19 13-16 9-16 7-19 6-19 3h-33l-25-5-23-7-18-8-21-11-17-12-8-9-3-7v-13l5-10 7-7 8-4h13l12 5 15 8 25 10 19 5 19 3h20l16-3 18-6 21-11 14-10 10-8 10-9 8-7 15-15 6-3z" fill="#1C2B31"/>
<path transform="translate(1672,1410)" d="m0 0h3v5l-2 6h-2l-3 9-2 4-2 9-3 4-2 10-3 5-1 6-12 20-9 14-13 18-3 5-8 1-3 8-3 1-1 3-1-3-5 1-3 2 2-6 5-5 4-8h2l2-4 4-6 5-4 3-5h2l1-5 2-5 3-3 4-10 4-4 3-6 4-10 5-4 3-7 10-14z" fill="#74B8CD"/>
<path transform="translate(615,667)" d="m0 0h4v7l-2 3-1 2-2 6-9 10-13 18-5 8h-4l-1 2v-2l-7 5-2-4 6-13 4-9 8-12 1-3 8-6 10-9z" fill="#74B8CE"/>
<path transform="translate(1549,1585)" d="m0 0 4 1-3 7v3l-5 6-3 1v-2l-8 3-4 5 1 5-4 4h-2l-1 3-3 2-10-1 2-7 8-7 3-4h2v-2l9-7 4-2z" fill="#74B8CD"/>
<path transform="translate(1416,1340)" d="m0 0h1v10l-4 11-1 9-5 12h-2l-2 5-3 5h-2l-2 4h-2l-2 4-4 5-6 4-7 3-5 5-7 1v-2l12-8 12-11 9-10 10-17 9-24z" fill="#7CC3DC"/>
<path transform="translate(1465,1650)" d="m0 0h2l2 3 5 1-4 6-6 3-1 2-4 1v-2l-3-1v-3l-3 1 1-3 8-4z" fill="#74B8CD"/>
<path transform="translate(1209,564)" d="m0 0 8 2h3l4 2v2l6-1 3 3-15 2h-10l2-2 6-1 2 1v-2l-10-2z" fill="#76BBD1"/>
<path transform="translate(1091,1349)" d="m0 0 7 6 8 8 6 4 3 1 3 3 14 7v1l-7-1-15-9-13-11-6-6z" fill="#7AC1D9"/>
<path transform="translate(1653,1466)" d="m0 0h2l-2 5-13 22-4 6-2-2 6-11 4-4 2-9 3-1v-2z" fill="#7EC6DF"/>
<path transform="translate(1591,1544)" d="m0 0h2l1 7h-2v2h-2l-1 3h-2l-1 3-3-2-1-2 5-7z" fill="#74B8CD"/>
<path transform="translate(1450,1663)" d="m0 0h3l-1 3-6 2-3 4-8 3-3-3 2-4h3l5-1z" fill="#74B9CE"/>
<path transform="translate(1632,1500)" d="m0 0 2 2-13 18-2-4 1-4 5-3h2l1-5z" fill="#7EC6DF"/>
<path transform="translate(1499,1627)" d="m0 0 3 1-2 3 6-1-1 5-2 2-4-1-2-3-2-1 1-4z" fill="#74B8CD"/>
<path transform="translate(1376,1407)" d="m0 0 2 1-6 8-9 2v-2l12-8z" fill="#7AC1D9"/>
<path transform="translate(1030,1352)" d="m0 0h1v17l1 7-4-4v-14z" fill="#7AC1D9"/>
<path transform="translate(1416,1340)" d="m0 0h1v10l-4 11-3 1 3-11 2-5z" fill="#7AC1D8"/>
<path transform="translate(659,631)" d="m0 0 5 1 1 4-2 2-5 1-1-4 2-1z" fill="#74B9CE"/>
<path transform="translate(1209,564)" d="m0 0 8 2 2 1v2h-7l-4-2z" fill="#74B8CD"/>
<path transform="translate(1355,1420)" d="m0 0h5l-1 2-10 4h-2v-2z" fill="#7AC1D8"/>
<path transform="translate(1116,1372)" d="m0 0 6 1 10 5v1l-7-1-9-5z" fill="#7AC0D7"/>
<path transform="translate(1530,1144)" d="m0 0 4 1-10 5h-2v-2z" fill="#7AC0D8"/>
</svg>
