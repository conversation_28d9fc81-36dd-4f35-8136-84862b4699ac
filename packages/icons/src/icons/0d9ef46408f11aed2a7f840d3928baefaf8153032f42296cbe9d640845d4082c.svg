<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
	<defs>
		<style>
			.cls-1 {
				fill: #f9f3ec;
			}

			.cls-2 {
				fill: #fe4040;
			}

			.cls-3 {
				fill: #fd7977;
			}

			.cls-4 {
				fill: #fd6a69;
			}

			.cls-5 {
				fill: #fbc0ba;
			}

			.cls-6 {
				fill: #fbbcb6;
			}

			.cls-7 {
				fill: #fbc4bf;
			}

			.cls-8 {
				fill: #fd706f;
			}

			.cls-9 {
				fill: #fca19d;
			}

			.cls-10 {
				fill: #faf4ec;
			}

			.cls-11 {
				fill: #feefef;
			}

			.cls-12 {
				fill: #feeeee;
			}

			.cls-13 {
				fill: #fc938f;
			}

			.cls-14 {
				fill: #fe6c6a;
			}

			.cls-15 {
				fill: #fec4c4;
			}

			.cls-16 {
				fill: #fe6969;
			}

			.cls-17 {
				fill: #fe7877;
			}

			.cls-18 {
				fill: #fe7777;
			}

			.cls-19 {
				fill: #fe7d7c;
			}

			.cls-20 {
				fill: #fea8a8;
			}

			.cls-21 {
				fill: #fe7170;
			}

			.cls-22 {
				fill: #fe403f;
			}
		</style>
	</defs>
	<path class="cls-1"
		d="M25.1752,12.4431C25.16,19.7032,19.746,25.3058,12.7209,25.17a11.7452,11.7452,0,0,1-8.6134-3.8612A11.7637,11.7637,0,0,1,.8261,12.8541,11.8053,11.8053,0,0,1,4.6939,4.1079,11.7921,11.7921,0,0,1,13.26.8305a11.79,11.79,0,0,1,8.5264,3.75A11.7024,11.7024,0,0,1,25.1752,12.4431Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-2"
		d="M17.76,8.1115a5.0047,5.0047,0,0,1,.488,2.3268,4.1489,4.1489,0,0,1-.9685,2.655.1313.1313,0,0,0-.009.1909c.0455.06.0823.1271.1229.1911a3.9285,3.9285,0,0,1,.3176.5541,5.968,5.968,0,0,1,.51,1.4268A4.7737,4.7737,0,0,1,17.9073,18.3a4.5876,4.5876,0,0,1-1.9334,2.1976,3.141,3.141,0,0,1-.63.2914,4.5848,4.5848,0,0,1-.9955.2194c-.1512.0135-.3033.0286-.4542.03-.5751.0072-1.15.0025-1.7257.0036-.0429,0-.0884-.0137-.1291.013q-1.5631.0152-3.1262.03c-.3661.0037-.7321.0095-1.0982.0135-.1619.0018-.1622.0009-.1634-.1684-.0044-.6157-.0077-1.2314-.0131-1.8471-.0032-.362-.0053-.7241-.0169-1.0859-.0038-.1185.0249-.16.1516-.16,1.0542-.0045,2.1083-.0164,3.1624-.026.1671-.0016.168-.0017.16-.17-.0383-.8638-.0791-1.7274-.1129-2.5914-.0042-.1064-.0517-.1138-.1345-.1129q-1.545.016-3.09.0286c-.1628.0014-.1625.0009-.164-.1616-.0091-.9817-.0163-1.9635-.03-2.9451-.0017-.1222.0306-.1554.1534-.1559.9857-.0038,1.9715-.0151,2.9572-.024.1633-.0015.1638-.0013.1573-.1694q-.0389-1.0066-.0784-2.0133c-.0075-.1889-.0205-.3775-.0266-.5663-.0031-.0954-.04-.128-.14-.1267-.9615.0126-1.9231.02-2.8847.0289-.1717.0016-.1719.0009-.173-.1686Q7.5226,7.607,7.5156,6.5508c-.0022-.2735-.0044-.5472-.0155-.82-.0042-.1044.0235-.1379.1324-.1383.8611-.0036,1.7221-.0137,2.5832-.0217.9334-.0087,1.8669-.0138,2.8-.0273A11.8519,11.8519,0,0,1,14.21,5.552a2.94,2.94,0,0,1,.5817.095,7.0155,7.0155,0,0,1,.9135.3177,3.1426,3.1426,0,0,1,.3408.1933A4.6265,4.6265,0,0,1,17.227,7.2271c.0584.0766.1138.1554.1706.2332a1.951,1.951,0,0,1,.144.2422l.0045.0192C17.629,7.8452,17.6835,7.9844,17.76,8.1115Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-3"
		d="M12.0389,21.0551c.04-.0416.0912-.0231.1371-.0233.5265-.003,1.0531-.0018,1.58-.0063a5.0743,5.0743,0,0,0,1.2951-.1644,1.4621,1.4621,0,0,1,.3206-.0963c.0128.0467-.0377.0263-.05.0467a3.6287,3.6287,0,0,1-.97.2127C13.5815,21.0761,12.81,21.06,12.0389,21.0551Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-4"
		d="M15.7075,5.9871a.6332.6332,0,0,1-.2543-.1005c-.2137-.1007-.4476-.1427-.6619-.24a2.4138,2.4138,0,0,1,.6614.1984c.0371.0143.0737.03.1105.0449C15.61,5.924,15.6877,5.9127,15.7075,5.9871Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-5"
		d="M15.3217,20.8115l.05-.0467c.2124-.0633.3972-.1887.6027-.2676A1.8968,1.8968,0,0,1,15.3217,20.8115Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-6"
		d="M17.7111,14.0294a5.9216,5.9216,0,0,1-.3176-.5542c.0921.0317.1024.1259.1429.1946A.98.98,0,0,1,17.7111,14.0294Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-7"
		d="M15.7075,5.9871,15.5632,5.89a1.2064,1.2064,0,0,1,.4824.2677C15.9224,6.1218,15.827,6.0306,15.7075,5.9871Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-8" d="M17.76,8.1115a1.4316,1.4316,0,0,1-.214-.39A.9754.9754,0,0,1,17.76,8.1115Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-9" d="M17.5416,7.7025a.6365.6365,0,0,1-.144-.2422A.3764.3764,0,0,1,17.5416,7.7025Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-10"
		d="M14.4044,8.9087a1.4937,1.4937,0,0,1,.3621,2.3409,1.2847,1.2847,0,0,1-.6528.3616,1.71,1.71,0,0,1-.346.0382c-.4222-.0027-.8444-.0038-1.2666.0011-.09.001-.1159-.0231-.1142-.114.0057-.3136.0075-.6274.001-.9409-.0114-.55-.0309-1.1-.0232-1.6508.0021-.149.0079-.1528.1569-.158.4258-.0148.8518-.0086,1.2778-.0121A1.3014,1.3014,0,0,1,14.4044,8.9087Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-11"
		d="M13.4177,16.5833a4.7824,4.7824,0,0,0-.29-.4084c-.1157-.1671-.2214-.3411-.34-.5063-.2239-.1876-.1667-.4524-.1905-.6948-.0041-.0409.02-.0579.0589-.057a.3558.3558,0,0,0,.0961,0c.2375-.0633.3539.0817.4533.2586a2.6918,2.6918,0,0,0,.2545.35c.0064.0051.0108.0121.0165.0175.048.045.0634.1513.1358.134.0635-.0151.0817-.11.1082-.1762.0281-.0417.0426-.0909.0731-.1313a1.1782,1.1782,0,0,0,.1046-.2081l.0494-.119c.0837-.2183.1373-.2579.3758-.2538.1406.0024.28-.0171.42-.013.0719.0022.1044.0347.0532.0964a3.2735,3.2735,0,0,0-.3321.5969c-.1324.2512-.2727.4981-.4042.75a.1588.1588,0,0,0,.0129.1965,1.1815,1.1815,0,0,1,.1736.2869,3.8867,3.8867,0,0,0,.3889.5591c.09.1369.2089.2522.3076.3831.0554.0736.0475.0975-.0431.12a2.2679,2.2679,0,0,1-.55.04.1753.1753,0,0,1-.1448-.0923,4.5074,4.5074,0,0,0-.2817-.3885,2.45,2.45,0,0,1-.2991-.4179C13.5559,16.7984,13.4871,16.6906,13.4177,16.5833Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-12"
		d="M13.08,16.6845c.1094-.1265.1066-.1248.1991.024.0759.1222.1582.24.24.3588.0333.0483.068.1019.0318.1543-.1262.1825-.1845.3989-.3008.5859a.1513.1513,0,0,1-.125.0813,5.154,5.154,0,0,1-.5271.024c-.0628-.0508-.0315-.108-.01-.1635.0948-.2465.2342-.472.335-.716A1.8806,1.8806,0,0,1,13.08,16.6845Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-13"
		d="M14.4044,8.9087a6.07,6.07,0,0,0-1.9349-.1c-.0909-.0016-.09.0581-.0892.1221.0073.3855-.0014.771.0141,1.1569.0188.47.0105.9409.0066,1.4114-.0008.1078.0247.1458.1393.1433.4061-.0089.8123-.0061,1.2185-.0088a1.3054,1.3054,0,0,0,.9452-.3523.0841.0841,0,0,1,.0625-.0315,1.0521,1.0521,0,0,1-.6971.3856,7.2184,7.2184,0,0,1-1.0463.0283c-.1807.0051-.3617-.0007-.5424.0036-.0772.0018-.1145-.0237-.1047-.1035a.0977.0977,0,0,0,0-.0121c.0117-.88-.0381-1.759-.027-2.639.0014-.1156.0349-.1529.1511-.1518.4541.0043.9083.0008,1.3624-.0022a1.2285,1.2285,0,0,1,.4794.1C14.3685,8.8689,14.3948,8.8779,14.4044,8.9087Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-14"
		d="M14.2362,16.7069c-.0623-.0981-.12-.1992-.1884-.2933a.144.144,0,0,1-.0148-.1759c.2631-.4589.4748-.946.7582-1.394a.2046.2046,0,0,0,.0115-.0474c-.2175.0122-.4286-.0042-.639.0236-.1306.0173-.1505.1428-.2163.2233.0279-.1866.1192-.2734.3024-.2663.1692.0065.3353-.0252.5035-.0254.04,0,.0843-.0067.1031.0413.0166.0424-.0163.0678-.0424.0914a.9344.9344,0,0,0-.1878.3057c-.1328.2693-.2769.5323-.4165.7978-.0392.0745-.0771.15-.1231.22a.1647.1647,0,0,0,.0173.2174.7163.7163,0,0,1,.1571.28Q14.25,16.7275,14.2362,16.7069Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-15"
		d="M14.2362,16.7069l.0248-.0013a9.6019,9.6019,0,0,0,.6946.9229c.0746.0912.0648.123-.05.1548a2.11,2.11,0,0,1-.5629.0394.1907.1907,0,0,1-.1549-.1015q-.1349-.1923-.27-.3842a.016.016,0,0,1,.0043-.026,1.0347,1.0347,0,0,1,.2474.32.3031.3031,0,0,0,.3378.1626c.13-.0165.2607-.0328.3912-.0475.068-.0077.073-.0372.0345-.0862-.1773-.226-.3553-.4516-.5314-.6786A.7474.7474,0,0,1,14.2362,16.7069Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-16"
		d="M13.08,16.6845a8.9729,8.9729,0,0,1-.4023.9013c-.0479.1-.1291.2-.08.327-.0889-.0208-.0559-.087-.04-.1331a1.5883,1.5883,0,0,1,.1069-.2522,6.2808,6.2808,0,0,0,.3244-.709C13.0074,16.7689,13.0178,16.7076,13.08,16.6845Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-17"
		d="M13.9222,17.311l-.0043.026a1.8237,1.8237,0,0,1-.31-.4242,1.3179,1.3179,0,0,1-.19-.3295c.1562.1681.2488.38.3868.561C13.8457,17.1983,13.883,17.2553,13.9222,17.311Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-18"
		d="M12.7875,15.6686a2.1019,2.1019,0,0,1,.34.5063c-.0777-.0275-.0937-.1065-.128-.1654A1.4762,1.4762,0,0,1,12.7875,15.6686Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-19"
		d="M13.7341,15.5021l-.0012.0119c-.0289.0728-.0509.1837-.1216.1886-.0824.0057-.117-.1012-.1514-.1763a.1112.1112,0,0,1,.0316.0157c.1256.1262.1255.1263.2134-.0418A.0166.0166,0,0,1,13.7341,15.5021Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-20"
		d="M13.898,15.1622a.3807.3807,0,0,1-.094.22.0349.0349,0,0,1-.0037-.0513A.4719.4719,0,0,1,13.898,15.1622Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-21" d="M13.8,15.3306l.0037.0513-.07.12L13.7049,15.5A.7134.7134,0,0,1,13.8,15.3306Z"
		transform="translate(-0.8248 -0.8276)" />
	<path class="cls-22"
		d="M13.1006,10.203c0-.12-.0026-.241.0008-.3613.0028-.0986.0519-.1371.1511-.1054.29.0928.5811.1833.8715.2756a.3307.3307,0,0,1,.0761.0358c.0882.0561.0886.1223-.0058.1709-.1283.0662-.261.1239-.3923.184-.1751.08-.35.1615-.5264.2385-.1347.0588-.1764.0308-.1813-.1128,0-.0081,0-.0161,0-.0241V10.203Z"
		transform="translate(-0.8248 -0.8276)" />
</svg>