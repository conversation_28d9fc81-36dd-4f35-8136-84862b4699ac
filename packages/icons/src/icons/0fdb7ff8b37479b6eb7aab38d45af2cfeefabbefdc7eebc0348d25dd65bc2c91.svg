<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="800" height="800" viewBox="0 0 800 800" xml:space="preserve">
  <desc>Created with Fabric.js 5.2.4</desc>
  <defs></defs>
  <rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
  <g transform="matrix(0 0 0 0 0 0)" id="1dba0bcf-0d4d-4fe7-b225-a45f0c609475"></g>
  <g transform="matrix(1 0 0 1 400 400)" id="ee56f04f-0124-45f1-9e95-d2518d4c3b88">
    <rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke" x="-400" y="-400" rx="0" ry="0" width="800" height="800" />
  </g>
  <g transform="matrix(11.32 0 0 11.32 399.93 399.93)" id="cf4b9375-f065-4b23-8afc-0ad3ec549362">
    <circle style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(224,187,36); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" cx="0" cy="0" r="35" />
  </g>
  <g transform="matrix(9.91 0 0 9.91 400.13 400.13)" id="9de173f1-3bbd-4cc0-b814-12870d29814f">
    <circle style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(208,216,17); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" cx="0" cy="0" r="35" />
  </g>
  <g transform="matrix(0.97 0.71 -1.07 1.47 127.66 421.81)" style="">
    <text xml:space="preserve" font-family="Raleway" font-size="105" font-style="normal" font-weight="900" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(35,88,8); fill-rule: nonzero; opacity: 1; white-space: pre;">
      <tspan x="-34.07" y="32.98">$</tspan>
    </text>
  </g>
  <g transform="matrix(2.12 0 0 8.75 400 400)" style="" id="7b875b71-e7fc-4920-aad4-ce3ee4d8e0d7">
    <text xml:space="preserve" font-family="Raleway" font-size="105" font-style="normal" font-weight="900" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(220,107,0); fill-rule: nonzero; opacity: 1; white-space: pre;">
      <tspan x="-34.07" y="32.98">$</tspan>
    </text>
  </g>
  <g transform="matrix(2.86 0.7 -0.32 1.64 599.28 411.43)" id="cfa6d22e-56a7-47da-8a55-798e4e1533ba">
    <polygon style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(213,39,39); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" points="-30.68,-37.41 30.68,0 -30.68,37.41 " />
  </g>
  <g transform="matrix(2.42 0 0 0.76 455.58 334.61)">
    <polygon style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(206,30,30); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" points="-30.68,-37.41 30.68,0 -30.68,37.41 " />
  </g>
  <g transform="matrix(1.23 0 0 1.1 483.61 394.78)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(221,40,40); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(1.91 0.19 -0.14 1.39 451.94 375.39)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(209,56,56); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(1.92 -1.36 0.72 1.17 223.77 354.59)" id="8975595e-72ab-49ec-8e51-0b6d23bfecb3">
    <polygon style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(185,29,29); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" points="-30.68,-37.41 30.68,0 -30.68,37.41 " />
  </g>
  <g transform="matrix(5.61 0 0 0.76 375 416.92)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(211,18,18); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(3.08 0 0 1.21 343.07 375.21)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,28,28); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(5.98 0 0 0.43 389.68 399.86)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(181,16,16); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(0.78 0 0 0.47 432.2 352.36)" id="594701a5-643f-453a-97a7-80da9baa6572">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(212,199,199); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(1.34 0.31 -0.08 0.46 497.84 361.73)" id="c5ab94e6-897d-4486-afe5-a3d6ff691d88">
    <polygon style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(212,199,199); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" points="-30.68,-37.41 30.68,0 -30.68,37.41 " />
  </g>
  <g transform="matrix(0 0 0 0 3978.02 45.51)">
    <g style=""></g>
  </g>
  <g transform="matrix(0 0 0 0 3978.02 45.51)">
    <g style=""></g>
  </g>
  <g transform="matrix(0 1.44 0.04 0 384.24 384.17)">
    <path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(19,19,19); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-50, -50)" d="M 94.75 50 C 94.75 56.213 89.514 61.25 83.054 61.25 L 16.946 61.25 C 10.486 61.25 5.25 56.213 5.25 50 L 5.25 50 C 5.25 43.787 10.486 38.75 16.945999999999998 38.75 L 83.053 38.75 C 89.514 38.75 94.75 43.787 94.75 50 L 94.75 50 z" stroke-linecap="round" />
  </g>
  <g transform="matrix(0.22 0 0 0.39 403.99 385.63)">
    <path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(101,101,101); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-50, -50)" d="M 94.75 50 C 94.75 56.213 89.514 61.25 83.054 61.25 L 16.946 61.25 C 10.486 61.25 5.25 56.213 5.25 50 L 5.25 50 C 5.25 43.787 10.486 38.75 16.945999999999998 38.75 L 83.053 38.75 C 89.514 38.75 94.75 43.787 94.75 50 L 94.75 50 z" stroke-linecap="round" />
  </g>
  <g transform="matrix(0 0 0 0 3978.02 45.51)">
    <g style=""></g>
  </g>
  <g transform="matrix(-1.34 -0.14 -0.01 0.07 442.25 325.49)">
    <path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(19,19,19); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-50, -50)" d="M 94.75 50 C 94.75 56.213 89.514 61.25 83.054 61.25 L 16.946 61.25 C 10.486 61.25 5.25 56.213 5.25 50 L 5.25 50 C 5.25 43.787 10.486 38.75 16.945999999999998 38.75 L 83.053 38.75 C 89.514 38.75 94.75 43.787 94.75 50 L 94.75 50 z" stroke-linecap="round" />
  </g>
  <g transform="matrix(1.36 -0.09 0.01 0.61 330.91 332.3)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(209,17,17); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(1.15 0 0 1.3 540.24 443.21)">
    <path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(65,56,56); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-50, -50)" d="M 49.975 12.826 C 70.509 12.826 87.174 29.492 87.174 50.025 C 87.174 70.509 70.50900000000001 87.174 49.97500000000001 87.174 C 29.492000000000008 87.174 12.826000000000008 70.50900000000001 12.826000000000008 50.025000000000006 C 12.826 29.492 29.492 12.826 49.975 12.826 z M 70.924 34.444 L 55.343 50.025 L 70.924 65.55499999999999 C 74.174 61.22099999999999 76.08200000000001 55.85499999999999 76.08200000000001 50.02499999999999 C 76.082 44.195 74.174 38.777 70.924 34.444 z M 65.557 70.973 L 49.975 55.391 L 34.447 70.92099999999999 C 38.78 74.172 44.145 76.08099999999999 49.975 76.08099999999999 C 55.806 76.081 61.223 74.172 65.557 70.973 z M 29.079 65.555 L 44.611000000000004 50.025000000000006 L 29.079 34.444 C 25.830000000000002 38.777 23.921 44.195 23.921 50.025000000000006 C 23.921 55.854 25.83 61.221 29.079 65.555 z M 34.394 29.078 L 49.974999999999994 44.661 L 65.55699999999999 29.078000000000003 C 61.222999999999985 25.828000000000003 55.80599999999999 23.92 49.97499999999999 23.92 C 44.145 23.919 38.781 25.828 34.394 29.078 z" stroke-linecap="round" />
  </g>
  <g transform="matrix(0.14 0 0 0.22 278.41 327.16)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(209,17,17); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(0.21 0 0 0.26 276.82 326.28)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(209,17,17); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(0.03 0 0 0.26 283.39 330.14)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(209,17,17); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(1.08 0 -0.02 0.61 318.79 339.84)" id="28c2fbcb-a1da-4c31-a6db-76cb00fd9f11">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(209,17,17); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(0.95 0 0 0.45 335.37 351.53)" id="594701a5-643f-453a-97a7-80da9baa6572">
    <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(212,199,199); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-37.165" y="-37.165" rx="0" ry="0" width="74.33" height="74.33" />
  </g>
  <g transform="matrix(1.15 0 0 1.3 214.84 440.61)">
    <path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(65,56,56); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-50, -50)" d="M 49.975 12.826 C 70.509 12.826 87.174 29.492 87.174 50.025 C 87.174 70.509 70.50900000000001 87.174 49.97500000000001 87.174 C 29.492000000000008 87.174 12.826000000000008 70.50900000000001 12.826000000000008 50.025000000000006 C 12.826 29.492 29.492 12.826 49.975 12.826 z M 70.924 34.444 L 55.343 50.025 L 70.924 65.55499999999999 C 74.174 61.22099999999999 76.08200000000001 55.85499999999999 76.08200000000001 50.02499999999999 C 76.082 44.195 74.174 38.777 70.924 34.444 z M 65.557 70.973 L 49.975 55.391 L 34.447 70.92099999999999 C 38.78 74.172 44.145 76.08099999999999 49.975 76.08099999999999 C 55.806 76.081 61.223 74.172 65.557 70.973 z M 29.079 65.555 L 44.611000000000004 50.025000000000006 L 29.079 34.444 C 25.830000000000002 38.777 23.921 44.195 23.921 50.025000000000006 C 23.921 55.854 25.83 61.221 29.079 65.555 z M 34.394 29.078 L 49.974999999999994 44.661 L 65.55699999999999 29.078000000000003 C 61.222999999999985 25.828000000000003 55.80599999999999 23.92 49.97499999999999 23.92 C 44.145 23.919 38.781 25.828 34.394 29.078 z" stroke-linecap="round" />
  </g>
</svg>