<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Creator: CorelDRAW X8 -->
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="21.6084mm" height="21.6084mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:pixelated; fill-rule:evenodd; clip-rule:evenodd"
viewBox="0 0 298 298"
 xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
   <![CDATA[
    .fil4 {fill:#FEFEFE}
    .fil5 {fill:#00EBF4;fill-rule:nonzero}
    .fil3 {fill:#FEFEFE;fill-opacity:0.290196}
    .fil2 {fill:#FEFEFE;fill-opacity:0.549020}
    .fil1 {fill:url(#id1)}
    .fil0 {fill:url(#id2)}
   ]]>
  </style>
    <clipPath id="id0">
     <rect x="71" y="79" width="156" height="139"/>
    </clipPath>
  <linearGradient id="id1" gradientUnits="userSpaceOnUse" x1="32.4998" y1="15.3919" x2="265.104" y2="278.785">
   <stop offset="0" style="stop-opacity:1; stop-color:#0449C9"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#00125C"/>
  </linearGradient>
  <linearGradient id="id2" gradientUnits="userSpaceOnUse" x1="268.522" y1="23.3729" x2="42.7588" y2="281.063">
   <stop offset="0" style="stop-opacity:1; stop-color:#48ECF2"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#0065B6"/>
  </linearGradient>
 </defs>
 <g id="Layer_x0020_1">
  <metadata id="CorelCorpID_0Corel-Layer"/>
  <circle class="fil0" cx="149" cy="149" r="149"/>
  <circle class="fil1" cx="149" cy="149" r="133"/>
  <path class="fil2" d="M107 31c11,-3 22,2 25,11 2,10 -5,20 -16,23 -11,3 -22,-2 -25,-11 -2,-10 5,-20 16,-23z"/>
  <path class="fil3" d="M64 63c9,-2 18,2 20,9 2,7 -4,15 -13,18 -9,2 -18,-2 -20,-9 -2,-7 4,-15 13,-18z"/>
  <g id="_1918573916112">
   <g style="clip-path:url(#id0)">
    <image x="71" y="79" width="156" height="139" xlink:href="ermoon logo svg_Images\ermoon logo svg_ImgID1.png"/>
   </g>
   <path class="fil4" d="M117 109l0 29 11 0c3,0 8,0 10,-2 3,-2 4,-3 5,-6 0,0 1,-1 1,-1l5 0c5,0 9,5 9,10l0 20c0,6 -4,10 -9,10l-5 0c0,0 -1,0 -1,-1 -1,-2 -2,-4 -5,-5 -2,-2 -7,-3 -10,-3l-11 0 0 29c13,0 27,0 40,0 4,0 8,-4 8,-8 0,-1 0,-1 1,-1l13 0c1,0 2,0 2,1l0 16c0,9 -7,16 -16,16l-42 0c-5,0 -10,-5 -10,-10l0 -11 -26 0c-6,0 -11,-5 -11,-11l0 -64c0,-6 5,-12 11,-12l26 0 0 -10c0,-6 5,-10 10,-10l42 0c9,0 16,7 16,16l0 15c0,1 -1,2 -2,2l-13 0c-1,0 -1,-1 -1,-2 0,-4 -4,-7 -8,-7 -13,0 -27,0 -40,0zm89 53l0 -15c0,-1 0,-1 0,-2 1,0 1,0 2,0 3,1 6,1 10,0 0,0 1,0 1,1 0,0 1,0 1,1l0 27c0,0 -1,1 -1,1 0,1 -1,1 -1,1 -7,-1 -12,-7 -12,-14zm-3 7c0,4 -4,7 -7,7 -4,0 -7,-3 -7,-7l0 -32c0,-1 0,-2 1,-2l8 0c1,0 1,1 2,1 0,2 1,4 2,5 0,0 1,1 1,1l0 27zm-31 5l0 -25c0,-7 5,-13 12,-14 0,0 1,1 1,1 0,0 1,1 1,1l0 25c0,7 -6,13 -13,14 0,0 -1,0 -1,-1 0,0 0,-1 0,-1zm46 -35c0,1 0,1 1,1 0,0 0,0 0,0 1,1 2,1 2,2 0,1 -1,1 -2,1 0,1 0,1 -1,1 -3,1 -8,1 -11,-1 -1,0 -1,0 -2,0 0,0 0,-1 -1,-1 -1,-1 -2,-2 -2,-3 0,0 -1,0 -1,0 0,-1 0,-1 -1,-2 0,0 0,0 0,0 0,0 0,-1 0,-1 -1,-1 -1,-1 -1,-2 -2,-2 -4,-6 -7,-7 0,-1 0,-1 0,-2 0,-1 0,-1 1,-1 7,-3 16,-3 22,1 2,1 3,3 4,5 1,2 2,5 3,8 0,0 -1,1 -1,1 0,1 -1,1 -1,1 -1,0 -1,0 -2,0 0,0 0,-1 0,-1z"/>
   <g>
    <g>
     <path class="fil5" d="M165 86l-42 0c-4,0 -8,4 -8,9l0 12c0,0 27,0 42,0 5,0 9,4 9,9l13 0 0 -14 0 -1c0,-8 -6,-15 -14,-15z"/>
     <path class="fil5" d="M165 211c8,0 14,-6 14,-14l0 -2 0 -14 -13 0c0,5 -4,9 -9,9 -15,0 -42,0 -42,0l0 13c0,5 4,8 8,8l42 0z"/>
     <path class="fil5" d="M87 190l28 0 0 -31 13 0c2,0 8,0 11,2 3,2 4,4 5,7l5 0c4,0 8,-4 8,-9l0 -20c0,-4 -4,-8 -8,-8l-5 0c-1,3 -2,5 -5,6 -3,3 -9,3 -11,3l-13 0 0 -1 0 0 0 -32 -28 0c-5,0 -9,4 -9,10l0 64c0,5 4,9 9,9z"/>
    </g>
    <g>
     <path class="fil5" d="M207 162l0 -15c5,1 9,0 11,0l0 27c-6,-1 -11,-6 -11,-12z"/>
     <path class="fil5" d="M201 169c0,3 -2,5 -5,5 -3,0 -6,-2 -6,-5l0 -32 8 0c1,2 2,4 3,5l0 27z"/>
     <path class="fil5" d="M173 174l0 -25c0,-6 5,-11 11,-12l0 25c0,6 -5,11 -11,12z"/>
     <path class="fil5" d="M206 141c0,0 0,0 -1,0 -1,-1 -1,-2 -2,-3 0,0 0,0 0,0 0,-1 -1,-1 -1,-2 0,0 0,0 0,0 0,0 -1,-1 -1,-1 0,-1 -1,-1 -1,-2 -4,-6 -7,-8 -7,-8 0,0 13,-4 21,2 2,1 3,2 4,4 2,3 2,7 2,7 0,0 -1,0 -2,0 -1,0 -2,-1 -4,-3 -1,0 -1,-1 -2,-1 -1,-2 -2,-3 -3,-4 -5,-3 -8,-2 -8,-2 0,0 3,0 6,3 1,1 2,2 3,3 0,1 1,1 1,1 0,0 0,1 0,1 1,0 1,1 1,1 3,3 5,4 6,4 1,1 1,1 1,1 0,0 0,0 -1,0 -2,1 -7,2 -11,0 0,0 0,0 -1,-1z"/>
    </g>
   </g>
  </g>
 </g>
</svg>
