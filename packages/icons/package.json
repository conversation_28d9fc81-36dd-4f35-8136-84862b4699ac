{"name": "@minotaur-ergo/icons", "version": "1.1.0", "description": "Asset Icons and details for minotaur wallet", "repository": "https://github.com/minotaur-ergo/minotaur", "license": "GPL-3.0", "author": "vorujack", "type": "module", "main": "dist/index.js", "scripts": {"add-icons": "tsx bin/add-icons.ts", "prettify": "npx prettier --ignore-unknown --write .", "fetch-details": "tsx bin/fetch-details.ts && npm run prettify", "build": "tsc && npm run add-icons"}, "files": ["dist"], "keywords": [], "dependencies": {"typescript": "^5.7.2"}, "devDependencies": {"@types/node": "^20.17.10", "ts-node": "^10.9.2", "tsx": "^4.19.2"}}