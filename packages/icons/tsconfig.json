{"compilerOptions": {"baseUrl": "./src", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSourceMap": true, "module": "esnext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "outDir": "dist", "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext", "jsx": "react", "declaration": true, "composite": true, "rootDir": "./src/"}, "include": ["src/**/*.ts"], "ts-node": {"esm": true}}