{"name": "@minotaur/types", "version": "1.0.0", "description": "Shared TypeScript types for Minotaur wallet", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["typescript", "types", "minotaur", "ergo", "wallet"], "author": "Minotaur Team", "license": "MIT", "devDependencies": {"typescript": "^5.0.0"}, "files": ["dist/**/*", "src/**/*"], "publishConfig": {"access": "public"}}