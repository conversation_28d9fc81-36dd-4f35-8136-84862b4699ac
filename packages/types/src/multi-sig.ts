/**
 * Multi-signature related types
 */

import { BaseEntity } from './common';
import { Token } from './transaction';

export enum MultiSigHintType {
  SIGNED = 'signed',
  SIMULATED = 'simulated'
}

export interface MultiSigHint {
  commit: string;
  proof: string;
  type: MultiSigHintType;
}

export interface MultiSigData {
  hints: Array<Array<MultiSigHint>>;
  secrets: Array<Array<string>>;
  partial?: any; // wasm.Transaction type
}

export interface MultiSigShareData {
  hints: Array<Array<string>>;
  secrets: Array<Array<string>>;
  txBytes: string;
  boxes: Array<any>;
}

export interface MultiSigDataShare {
  tx: string;
  boxes: Array<string>;
  hints: Array<Array<string>>;
}

export interface MultiSigDataHint {
  commit: string;
  proof: string;
  type: MultiSigDataHintType;
}

export enum MultiSigDataHintType {
  SIMULATED = 'simulated',
  REAL = 'real',
}

export interface MultiSigRow extends BaseEntity {
  txId: string;
  txBytes: string;
  updateTime: number;
  inputs: MultiSigInput[];
  hints: MultiSigHint[];
}

export interface MultiSigBriefRow {
  rowId: number;
  txId: string;
  committed: number;
  signed: number;
  ergIn: bigint;
  ergOut: bigint;
  tokensIn: number;
  tokensOut: number;
}

export interface MultiSigDataRow {
  rowId: number;
  requiredSign: number;
  tx: any; // wasm.ReducedTransaction
  dataBoxes: Array<any>; // Array<wasm.ErgoBox>
  boxes: Array<any>; // Array<wasm.ErgoBox>
  hints: Array<Array<MultiSigDataHint>>;
}

export interface MultiSigInput extends BaseEntity {
  address: string;
  value: string;
  tokens: Token[];
  boxId: string;
  txId: number;
}

export interface MultiSigTx extends BaseEntity {
  txId: string;
  txBytes: string;
  updateTime: number;
  rows: MultiSigRow[];
}

export interface CommitResult {
  hints: Array<Array<MultiSigHint>>;
  secrets: Array<Array<string>>;
  updateTime: number;
  rowId?: number;
  changed: boolean;
}

export interface DetachedCommitments {
  known: Array<Array<string>>;
  own: Array<Array<string>>;
}

// Multi-sig context types
export interface MultiSigMyAction {
  committed: boolean;
  signed: boolean;
}

export enum MultiSigStateEnum {
  COMMITMENT = 'commitment',
  SIGNING = 'signing',
  COMPLETED = 'completed',
}

export interface MultiSigContextType {
  hints: Array<Array<MultiSigDataHint>>;
  rowId: number;
  requiredSign: number;
  password: string;
  setPassword: (password: string) => unknown;
  setHints: (hints: Array<Array<MultiSigDataHint>>, updateTime: number) => unknown;
  signed?: any; // Transaction
  setSigned: (signed: any) => unknown;
}

// Transaction hint bag types for Ergo
export interface TxHintPublicKey {
  op: string;
  h: string;
}

export interface TxSingleSecretHint {
  hint: string;
  challenge: string;
  position: string;
  proof: string;
  pubkey: TxHintPublicKey;
}

export interface TxSinglePublicHint {
  hint: string;
  secret?: string;
  pubkey: TxHintPublicKey;
  type: string;
  a: string;
  position: string;
}

export interface TxPublicHint {
  [key: string]: Array<TxSinglePublicHint>;
}

export interface TxSecretHint {
  [key: string]: Array<TxSingleSecretHint>;
}

export interface TxHintBag {
  publicHints: TxPublicHint;
  secretHints: TxSecretHint;
}
