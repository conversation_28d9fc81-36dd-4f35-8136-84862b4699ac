/**
 * DApp related types
 */

import React from 'react';

export interface DAppType {
  name: string;
  description: string;
  readme?: React.ReactNode;
  icon?: React.ReactNode;
  color: string;
  id: string;
  networks: Array<any>; // wasm.NetworkPrefix
  component: React.ComponentType<DAppPropsType>;
}

export interface AssetInfo {
  amount: bigint;
  id: string;
  name: string;
  decimal: number;
}

export interface CoveringResult {
  covered: boolean;
  boxes: any; // wasm.ErgoBoxes
}

export type UnsignedGeneratedTx = {
  tx: any; // wasm.UnsignedTransaction | wasm.ReducedTransaction
  boxes: any; // wasm.ErgoBoxes
  dataBoxes?: any; // wasm.ErgoBoxes
};

interface DAppPropsType {
  walletId: number;
  getAddresses: () => Promise<Array<string>>;
  getDefaultAddress: () => Promise<string>;
  getAssets: () => Promise<Array<AssetInfo>>;
  getCoveringForErgAndToken: (
    amount: bigint,
    tokens: Array<{ id: string; amount: bigint }>,
    address?: string,
  ) => Promise<CoveringResult>;
  chain: any; // ChainTypeInterface
  getTokenAmount: (tokenId?: string) => Promise<bigint>;
  signAndSendTx: (tx: UnsignedGeneratedTx) => unknown;
  showNotification: (message: string, type: any) => unknown; // VariantType
  createChangeBox: (
    inputs: any, // wasm.ErgoBoxes
    outputs: any, // wasm.ErgoBoxCandidates
    fee: bigint,
    height: number,
  ) => Promise<Array<any>>; // Array<wasm.ErgoBoxCandidate>
}
