/**
 * ErgoPay related types
 */

export enum MultiAddressSupportedEnum {
  REQUIRED = 'required',
  OPTIONAL = 'optional',
  NONE = 'none',
}

export enum ErgoPaySeverityEnum {
  INFORMATION = 'information',
  WARNING = 'warning',
  ERROR = 'error',
}

export interface ErgoPayResponse {
  address?: string;
  reducedTx?: string;
  message?: string;
  messageSeverity?: ErgoPaySeverityEnum;
  replyTo?: string;
}

export interface MessageResponseType {
  allowedWallets: Array<any>; // StateWallet
  title: string;
  description: Array<string>;
  severity: string;
  needAddress: boolean;
  replyTo: string;
  failed: boolean;
}

export interface MultipleAddressResponse {
  addresses: Array<string>;
  message: string;
  replyTo: string;
}

export interface LoadedErgoPayResponse {
  response: ErgoPayResponse;
  walletIds: Array<number>;
  boxes: Array<any>; // wasm.ErgoBox | undefined
}

export interface InternalBoxLoadedData {
  [walletId: string]: Array<any>; // wasm.ErgoBox | undefined
}
