/**
 * Common types used across the application
 */

export interface BaseEntity {
  id: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export type NetworkType = 'mainnet' | 'testnet';

export interface NetworkConfig {
  type: NetworkType;
  nodeUrl: string;
  explorerUrl: string;
  name: string;
}

// Transaction types
export interface TokenSpent {
  [tokenId: string]: bigint;
}

export interface TotalSpent {
  value: bigint;
  tokens: TokenSpent;
}

export interface TokenType {
  tokenId: string;
  amount: bigint;
}

// Window types
declare global {
  interface Window {
    Buffer: unknown;
    SQL: unknown;
    electronApi: {
      openUrl: (url: string) => unknown;
      writeFile: (filepath: string, data: string) => Promise<unknown>;
      readFile: (filepath: string) => Promise<string>;
    };
  }
}
