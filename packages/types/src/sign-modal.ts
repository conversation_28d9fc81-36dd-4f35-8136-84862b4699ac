/**
 * Sign modal related types
 */

interface SignActionTemplated<T> {
  signTx: () => Promise<T>;
}

interface DisplayBoxAction {
  displayBoxes: () => Promise<unknown>;
}

export type SignAndDisplayAction = SignActionTemplated<unknown> & DisplayBoxAction;

export type SignAction = SignActionTemplated<any>; // wasm.Transaction

export enum RegisterKeys {
  R4 = 4,
  R5 = 5,
  R6 = 6,
  R7 = 7,
  R8 = 8,
  R9 = 9,
}

export interface ReceiverType {
  address: string;
  amount: bigint;
  tokens: ReceiverTokenType[];
  registers?: {
    [register in RegisterKeys]: any; // wasm.Constant
  };
}

export interface ReceiverTokenType {
  id: string;
  amount: bigint;
}

export interface BoxContent {
  address: string;
  amount: bigint;
  tokens: Array<ReceiverTokenType>;
}
