/**
 * Network related types
 */

export enum BoxStatus {
  SPEND = 'spend',
  INVALID = 'invalid',
  AVAILABLE = 'available',
}

export interface BoxSpendDetail {
  boxId: string;
  status: BoxStatus;
  spend?: {
    tx: string;
    height: number;
    timestamp: number;
    blockId: string;
    index: number;
  };
}

export interface SpendDetail {
  tx: string;
  height: number;
  timestamp: number;
  index: number;
}

export interface ChainTypeInterface {
  readonly prefix: any; // NetworkPrefix
  readonly label: string;

  getNetwork: () => any; // AbstractNetwork
  getExplorerFront: () => string;
  fakeContext: () => any; // wasm.ErgoStateContext
}

export interface BalanceInfo {
  nanoErgs: bigint;
  tokens: Array<{
    id: string;
    amount: bigint;
  }>;
}
