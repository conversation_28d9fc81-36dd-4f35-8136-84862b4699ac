/**
 * Transaction related types
 */

export interface UnsignedTransaction {
  inputs: TransactionInput[];
  outputs: TransactionOutput[];
  fee: string;
  changeAddress?: string;
}

export interface SignedTransaction extends UnsignedTransaction {
  signature: string;
  txId: string;
}

export interface TransactionInput {
  boxId: string;
  value: string;
  address: string;
  tokens: Token[];
  additionalRegisters?: Record<string, any>;
}

export interface TransactionOutput {
  value: string;
  address: string;
  tokens: Token[];
  additionalRegisters?: Record<string, any>;
  creationHeight?: number;
}

export interface Token {
  tokenId: string;
  amount: string;
  decimals?: number;
  name?: string;
  symbol?: string;
}

export interface TransactionRequest {
  outputs: TransactionOutput[];
  fee?: string;
  changeAddress?: string;
  inputs?: TransactionInput[];
}

export interface TransactionStatus {
  txId: string;
  status: 'pending' | 'confirmed' | 'failed';
  confirmations: number;
  blockHeight?: number;
  timestamp?: number;
}

export interface Box {
  boxId: string;
  value: string;
  address: string;
  tokens: Token[];
  additionalRegisters?: Record<string, any>;
  creationHeight: number;
  confirmations: number;
}
