/**
 * Wallet related types
 */

import { BaseEntity, NetworkType } from './common';

export interface WalletConfig {
  name: string;
  type: 'standard' | 'readonly' | 'ledger';
  networkType: NetworkType;
  addresses: string[];
}

export interface Wallet extends BaseEntity {
  name: string;
  type: 'standard' | 'readonly' | 'ledger';
  networkType: NetworkType;
  addresses: WalletAddress[];
  balance?: WalletBalance;
}

export interface WalletAddress extends BaseEntity {
  address: string;
  path: string;
  index: number;
  walletId: number;
  balance?: AddressBalance;
}

export interface WalletBalance {
  confirmed: string;
  unconfirmed: string;
  total: string;
  tokens: TokenBalance[];
}

export interface AddressBalance {
  confirmed: string;
  unconfirmed: string;
  total: string;
  tokens: TokenBalance[];
}

export interface TokenBalance {
  tokenId: string;
  amount: string;
  decimals?: number;
  name?: string;
  symbol?: string;
}

export interface WalletTransaction {
  txId: string;
  blockId?: string;
  timestamp: number;
  confirmations: number;
  inputs: TransactionInput[];
  outputs: TransactionOutput[];
  fee: string;
  status: 'pending' | 'confirmed' | 'failed';
}

export interface TransactionInput {
  boxId: string;
  address: string;
  value: string;
  tokens: TokenBalance[];
}

export interface TransactionOutput {
  boxId: string;
  address: string;
  value: string;
  tokens: TokenBalance[];
  creationHeight: number;
}
