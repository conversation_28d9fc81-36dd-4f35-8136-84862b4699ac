image: node:20.12

stages:
  - installation
  - build
  - type_check
  - lint
  - test

installation:
  stage: installation
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: push
    paths:
      - node_modules
      - '**/node_modules'
  script:
    - npm ci

build:
  stage: build
  cache:
    - key: $CI_COMMIT_REF_NAME
      policy: pull-push
      paths:
        - node_modules
        - '**/node_modules'
        - '**/dist'
  script:
    - ./cli/build.sh

type_check:
  stage: type_check
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - apps/wallet/node_modules
  script:
    - npm run type-check

lint:
  stage: lint
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - apps/wallet/node_modules
  script:
    - npm run lint

test:
  stage: test
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - apps/wallet/node_modules
  script:
    - npm run coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  # artifacts:
  #   reports:
  #     coverage_report:
  #       coverage_format: cobertura
  #       path: coverage/cobertura-coverage.xml
