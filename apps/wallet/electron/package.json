{"name": "minotaur", "version": "1.0.0", "description": "A multiplatform wallet for ergo.", "author": {"name": "minotaur-ergo", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/minotaur-ergo/minotaur"}, "license": "MIT", "main": "build/src/index.js", "scripts": {"postinstall": "patch-package", "clean": "rimraf ./dist && rimraf ./build", "build": "tsc && electron-rebuild", "electron:start-live": "node ./live-runner.js", "electron:start": "npm run build && electron --inspect=5858 ./", "electron:pack": "npm run build && electron-builder build --dir -c ./electron-builder.config.json", "electron:make": "npm run build && electron-builder build -c ./electron-builder.config.json -p never", "electron:publish": "npm run build && electron-builder build -c ./electron-builder.config.json -p always"}, "dependencies": {"@capacitor-community/electron": "^5.0.1", "@capacitor-community/sqlite": "^5.6.1", "better-sqlite3-multiple-ciphers": "^9.3.0", "chokidar": "~3.5.3", "crypto-js": "^4.2.0", "electron-is-dev": "~2.0.0", "electron-json-storage": "^4.6.0", "electron-serve": "~1.3.0", "electron-unhandled": "~4.0.1", "electron-updater": "^6.1.7", "electron-window-state": "^5.0.3", "jszip": "^3.10.1", "node-fetch": "2.6.7", "patch-package": "^8.0.0"}, "devDependencies": {"@electron/rebuild": "^3.6.0", "@types/better-sqlite3": "^7.6.8", "@types/crypto-js": "^4.2.2", "@types/electron-json-storage": "^4.5.4", "electron": "^28.1.4", "electron-builder": "~24.9.1", "typescript": "^5.3.3"}, "keywords": ["capacitor", "electron"]}