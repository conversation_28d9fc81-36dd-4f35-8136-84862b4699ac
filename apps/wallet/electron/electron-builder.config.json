{"appId": "io.github.minotaur_ergo.minotaur", "directories": {"buildResources": "resources"}, "files": ["assets/**/*", "build/**/*", "capacitor.config.*", "app/**/*"], "publish": {"provider": "github"}, "nsis": {"allowElevation": true, "oneClick": false, "allowToChangeInstallationDirectory": true}, "win": {"target": "nsis", "icon": "assets/appIcon.ico"}, "mac": {"category": "your.app.category.type", "target": "dmg", "icon": "assets/appIcon.icns"}, "linux": {"target": ["snap", "AppImage"]}}