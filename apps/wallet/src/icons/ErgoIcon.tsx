import { CSSProperties } from 'react';

const ErgoIcon = (props: { styles: CSSProperties }) => (
  <svg
    style={props.styles}
    version="1.0"
    viewBox="0 0 1080 1080"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M526 1.2C522 2.3 169 153 161.9 156.5c-4.8 2.5-10.8 8.4-13.3 13.4-2.5 4.7-146.2 363.2-147.5 367.6-1.4 5.1-1.4 10.9 0 16 1.2 4.2 151.4 356.6 155.4 364.6 2.5 4.9 8.5 10.8 13.5 13.4 4.8 2.5 363 146.1 367.5 147.4 5.1 1.4 10.9 1.4 16 0 3.9-1.1 356.4-151.3 363.7-155 5.3-2.7 10.5-7.5 13.3-12.3 3-5.2 147-363.9 148.6-370.1 2.6-10.5 5.2-3.9-76.7-195.6-42.1-98.7-77.7-181.5-78.9-184-2.5-4.8-8.4-10.8-13.4-13.4C905.3 146.1 547 2.4 542.5 1.1c-4.8-1.3-11.7-1.3-16.5.1zm165.5 160.7c86.2 34.6 156.8 63.1 156.9 63.3.2.2 30 70 66.4 155.2l66.1 154.9-62.5 155.6C884 776.5 855.6 847 855.2 847.6c-.4.6-70.4 30.8-155.5 67.2l-154.9 66.1-156.5-62.8-156.6-62.9-66.3-155.1-66.3-155.2 62.8-156.6 62.9-156.5 154.8-66.3c85.2-36.5 154.9-66.3 155-66.4.1-.1 70.6 28.2 156.9 62.8z" />
    <path d="M366 348.3v44.3l68 70.9c37.4 39 68 71.2 68 71.7s-30.8 34.9-68.5 76.6L365 687.5l.3 44 .2 44h349l.3-48.2.2-48.3-104.6-.2-104.7-.3 65.7-72.3 65.7-72.3L574 468c-34.6-36.3-63-66.2-63-66.5 0-.3 45.9-.5 102-.5h102v-97H366v44.3z" />
  </svg>
);

export default ErgoIcon;
