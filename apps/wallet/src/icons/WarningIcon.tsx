import { CSSProperties } from 'react';

const ErgoIcon = (props: { styles: CSSProperties }) => (
  <svg
    style={props.styles}
    version="1.0"
    viewBox="0 0 80 80"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M30.6 12.6c-7.1 2.2-15.9 11.2-18 18.4-3.4 11.5-1.2 20.6 7.3 29.1S37.5 70.8 49 67.4c7.5-2.2 16.2-10.9 18.4-18.4 3.4-11.5 1.2-20.6-7.3-29.1-8.5-8.4-18-10.8-29.5-7.3zm22.6 4.1c9.2 5.1 14.6 15.5 13.5 26.1-1.5 14.5-14.9 25.4-29.5 23.9-14.5-1.5-25.4-14.9-23.9-29.5C14.4 27 22.5 17.1 32.4 14c5.7-1.7 15-.5 20.8 2.7z" />
    <path d="M39 17c0 .5.5 1 1 1 .6 0 1-.5 1-1 0-.6-.4-1-1-1-.5 0-1 .4-1 1zM30.5 19c.3.5.8 1 1.1 1 .2 0 .4-.5.4-1 0-.6-.5-1-1.1-1-.5 0-.7.4-.4 1zM48 19.1c0 .5.5.7 1 .4.6-.3 1-.8 1-1.1 0-.2-.4-.4-1-.4-.5 0-1 .5-1 1.1zM26.5 21c.3.5.8 1 1.1 1 .2 0 .4-.5.4-1 0-.6-.5-1-1.1-1-.5 0-.7.4-.4 1zM52 21.1c0 .5.5.7 1 .4.6-.3 1-.8 1-1.1 0-.2-.4-.4-1-.4-.5 0-1 .5-1 1.1zM23 24.1c0 .5.5.7 1 .4.6-.3 1-.8 1-1.1 0-.2-.4-.4-1-.4-.5 0-1 .5-1 1.1zM55.5 24c.3.5.8 1 1.1 1 .2 0 .4-.5.4-1 0-.6-.5-1-1.1-1-.5 0-.7.4-.4 1zM36 27.7c0 1.6.3 5.8.7 9.5.6 6.6.7 6.8 3.3 6.8 2.6 0 2.7-.2 3.3-6.8C44.5 24.9 44.6 25 40 25c-3.7 0-4 .2-4 2.7zm5.5 6.5c-.7 7.1-1.1 8.6-1.9 7.7-.2-.2-.7-3.7-1.1-7.7-.7-7-.6-7.2 1.5-7.2s2.2.2 1.5 7.2zM20 27c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM58.5 27c-.3.5-.1 1 .4 1 .6 0 1.1-.5 1.1-1 0-.6-.2-1-.4-1-.3 0-.8.4-1.1 1zM18 31c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM60.5 31c-.3.5-.1 1 .4 1 .6 0 1.1-.5 1.1-1 0-.6-.2-1-.4-1-.3 0-.8.4-1.1 1zM16 40c0 .5.5 1 1 1 .6 0 1-.5 1-1 0-.6-.4-1-1-1-.5 0-1 .4-1 1zM62 40c0 .5.5 1 1 1 .6 0 1-.5 1-1 0-.6-.4-1-1-1-.5 0-1 .4-1 1zM37 48.5c-1.5 1.8-1 4.5 1.3 5.9 3.4 2.2 7.3-2.7 4.7-5.9-1.6-1.9-4.4-1.9-6 0zm4.6 1.6c1 1.7-1.3 3.6-2.7 2.2-1.2-1.2-.4-3.3 1.1-3.3.5 0 1.2.5 1.6 1.1zM18 49.1c0 .5.5.7 1 .4.6-.3 1-.8 1-1.1 0-.2-.4-.4-1-.4-.5 0-1 .5-1 1.1zM60.5 49c.3.5.8 1 1.1 1 .2 0 .4-.5.4-1 0-.6-.5-1-1.1-1-.5 0-.7.4-.4 1zM20 53.1c0 .5.5.7 1 .4.6-.3 1-.8 1-1.1 0-.2-.4-.4-1-.4-.5 0-1 .5-1 1.1zM58.5 53c.3.5.8 1 1.1 1 .2 0 .4-.5.4-1 0-.6-.5-1-1.1-1-.5 0-.7.4-.4 1zM23 56c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM55.5 56c-.3.5-.1 1 .4 1 .6 0 1.1-.5 1.1-1 0-.6-.2-1-.4-1-.3 0-.8.4-1.1 1zM26.5 59c-.3.5-.1 1 .4 1 .6 0 1.1-.5 1.1-1 0-.6-.2-1-.4-1-.3 0-.8.4-1.1 1zM52 59c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM30.5 61c-.3.5-.1 1 .4 1 .6 0 1.1-.5 1.1-1 0-.6-.2-1-.4-1-.3 0-.8.4-1.1 1zM48 61c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM39 63c0 .5.5 1 1 1 .6 0 1-.5 1-1 0-.6-.4-1-1-1-.5 0-1 .4-1 1zM10 22.1c-6.5 10.6-6.5 25.2 0 35.8 3.3 5.3 3.5 2.8.4-3.6-2.5-5-2.9-7-2.9-14.3 0-7.3.4-9.3 2.9-14.3 3.1-6.4 2.9-8.9-.4-3.6zM67 19.5c0 .5 1.2 3.3 2.8 6.4 2.3 4.8 2.7 6.9 2.7 14.1 0 7.3-.4 9.3-2.9 14.3-3.1 6.4-2.9 8.8.4 3.6 5.5-9 6.3-21.6 2-31.6-2-4.7-5-8.8-5-6.8z" />
  </svg>
);

export default ErgoIcon;
