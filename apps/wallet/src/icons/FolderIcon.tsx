import { CSSProperties } from 'react';

const ErgoIcon = (props: { styles: CSSProperties }) => (
  <svg
    style={props.styles}
    version="1.0"
    viewBox="0 0 512 512"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M150.2 56.3c-1.9 2-2.2 3.5-2.2 10.1 0 6.9.3 7.9 2.6 10.3 2 2 3.1 2.4 5.7 1.9 3.7-.7 6.7-3.9 6.7-7.1 0-1.8.7-2.4 3.5-2.9 6.3-1.2 8.5-7.7 4-12.1-2.1-2.2-3.3-2.5-10.3-2.5-6.9 0-8.1.3-10 2.3zM190 56c-3 3-2.7 8.3.6 10.9 2.4 1.9 4 2.1 14.6 2.1 11.3 0 12-.1 14.3-2.5 3.2-3.1 3.3-7.4.2-10.3-2.1-2-3.4-2.2-15-2.2-11.4 0-12.9.2-14.7 2zM239.2 56.3c-2.9 3.1-2.8 7.2.3 10.2 2.3 2.4 3 2.5 14.4 2.5 11 0 12.2-.2 14.5-2.3 3.6-3.2 3.9-7.4.6-10.7-1.8-1.8-3.3-2-14.8-2-12.1 0-13 .1-15 2.3zM288.5 56.5c-3 2.9-3.2 6.4-.6 9.6 1.9 2.3 2.4 2.4 15.5 2.4 14.9 0 16-.4 17.2-6.2.5-2.6.1-3.7-1.9-5.7-2.5-2.5-3-2.6-15.2-2.6-12 0-12.7.1-15 2.5zM338 56c-3.3 3.3-3 7.5.6 10.7 2.3 2.1 3.5 2.3 14.5 2.3 11.4 0 12.1-.1 14.4-2.5 3.2-3.1 3.3-7.4.2-10.3-2.1-2-3.4-2.2-15-2.2-11.4 0-12.9.2-14.7 2zM387.2 56.3c-2.9 3.1-2.8 7.2.3 10.2 2.3 2.4 3 2.5 14.3 2.5 10.6 0 12.2-.2 14.6-2.1 3.3-2.6 3.6-7.9.6-10.9-1.8-1.8-3.3-2-14.8-2-12.1 0-13 .1-15 2.3zM436.5 56.5c-4.5 4.4-2.3 10.9 4 12.1 2.8.5 3.5 1.1 3.5 2.9 0 3.2 3 6.4 6.7 7.1 2.6.5 3.7.1 5.7-1.9 2.4-2.4 2.6-3.4 2.6-10.5 0-6.9-.3-8.1-2.3-10-2-1.9-3.5-2.2-10.1-2.2-6.8 0-8 .3-10.1 2.5zM79.3 59c-5.2 2.2-5.6 7.6-.7 13l3.6 4-3.6 4.2C74.5 85 74 88.6 77 91.8c3.2 3.4 8.2 2.9 12.8-1.3l3.9-3.4 3.3 3.4c8.9 9.2 19.1-.8 10.6-10.2l-3.4-3.8 3.5-4c4.2-4.9 4.3-9.7.1-12.8-3.3-2.5-6.5-1.8-11.3 2.5l-3.6 3.1-3.2-3.1c-3.6-3.4-7.1-4.6-10.4-3.2zM258.9 92c-9.3 1.6-19.1 8.6-23.4 16.9l-1.6 3.1H163v-7.5c0-6.7-.3-7.9-2.5-10-1.3-1.4-3.6-2.5-5-2.5s-3.7 1.1-5 2.5c-2.2 2.1-2.5 3.3-2.5 9.9v7.4l-13.8.4c-13.2.3-14.1.4-18 3.1-7.6 5-8.2 7.4-8.2 33v22.4l-6.9.6C83.5 172.8 71 186.1 71 203.5c0 17.4 12.8 31 30.3 32.3l6.7.5v7.3c0 6.1.4 7.8 2 9.4 2.5 2.5 7.1 2.6 10.1.1 2.2-1.8 2.4-2.8 2.7-12.7.4-10.8.4-10.9 3.9-14.8 11.6-12.8 11.6-31.4-.1-44.3l-3.6-4v-24c0-17.2.3-24.2 1.2-25.1.9-.9 14.4-1.2 54.5-1.2 46.1 0 53.3.2 53.3 1.5 0 3.5 4.4 12.7 8.1 16.8 2.3 2.6 6.6 5.9 10.1 7.6 5.1 2.6 7.3 3.1 13.4 3.1 8.1 0 15.9-2.4 20.9-6.5l2.9-2.4 8.6 8.3c6.5 6.4 9.6 8.7 12.8 9.5 2.8.8 12.9 1.1 30.3.9 26-.3 26.1-.3 28-2.7 2.5-3 2.4-7.6-.1-10.1-1.9-1.9-3.3-2-28.1-2h-26.2l-8.7-8.6-8.8-8.5 1-4.2c2.2-9.7-2.7-23.7-10.5-30.1-6.8-5.4-9.1-6.3-20.2-8-1.1-.2-4.1 0-6.6.4zm13.3 16.7c9.8 4.9 12.6 17.9 5.5 25.9-4.1 4.8-7.7 6.4-14 6.4-10.2 0-17.4-8.1-16.5-18.6 1.1-12.6 13.6-19.5 25-13.7zm-161.5 79.2c13 5.9 13 25.3 0 31.2-5.5 2.4-10.1 2.4-15.4-.2-12.3-6-12.4-24.7-.1-30.8 5.3-2.6 10-2.6 15.5-.2zM446.1 97.6c-1.9 2.4-2.1 4-2.1 14.9s.2 12.5 2.1 14.9c2.7 3.4 7.7 3.6 10.7.3 2-2.1 2.2-3.4 2.2-15.3 0-12.3-.1-13.2-2.3-15.2-3.3-3.1-8-2.9-10.6.4z" />
    <path d="M150.5 141.5c-2.4 2.3-2.5 3-2.5 14.5s.1 12.2 2.5 14.5c1.3 1.4 3.6 2.5 5 2.5s3.7-1.1 5-2.5c2.4-2.3 2.5-3 2.5-14.5s-.1-12.2-2.5-14.5c-1.3-1.4-3.6-2.5-5-2.5s-3.7 1.1-5 2.5zM446.4 148.5l-2.5 2.5h-48.8l-2 2.6c-2.4 3-2.6 4.3-1 7.7 1.9 4.3 4.6 4.7 28.9 4.7h23v4.5c0 5.7 1.6 9.1 4.9 10.5 3.3 1.3 7.5 0 9-2.9.6-1.1 1.1-4.2 1.1-7V166h17.9c13.8 0 18.1.3 19 1.4.8 1 1 36.1.9 131.5l-.3 130.1-21.2-26-21.1-26.1 2.4-2.4c2.1-2.1 2.4-3.3 2.4-10s-.3-7.9-2.5-10c-4.8-4.8-11.4-2-12.4 5.3l-.6 4.1-26.2-32.3c-19.5-24-27.3-32.9-30-34.2-3.3-1.6-6.7-1.9-24.7-2.2l-20.8-.3.7-5.5c2-15.9-8-33-23.2-40.2-5.1-2.4-7-2.7-15.8-2.7s-10.7.3-15.8 2.7c-15.3 7.2-25.2 24.3-23.2 40.3l.7 5.5H163v-5c0-4.1-.5-5.6-2.5-7.5-3.1-3.2-6.9-3.2-10 0-2 1.9-2.5 3.4-2.5 7.5v5h-24.8l-.4-6.5c-.5-7.6-2.8-10.8-7.7-10.8-5.3 0-7.1 2.6-7.1 10.5v6.8H63.3c-29.3 0-46.2.4-48.8 1.1C3 299.3-3.3 312.1 1.8 322c1 1.9 25.3 32.5 54.2 67.9 43.9 54 53 64.6 56.4 66.2 3.9 1.8 9 1.9 105.9 1.9 69.9 0 102.5-.3 103.8-1.1 4.8-2.5 4.4-10.5-.7-12.8-1.8-.8-30.4-1.1-102.7-1.1H118.5l-51.4-63.2c-28.3-34.8-51.6-64-51.8-64.9-.3-.9.3-2.3 1.2-3.3C18 310.1 32.3 310 199 310h181l2.9 3.2c1.6 1.8 25.9 31.6 54.1 66.3l51.2 63-68 .5c-65.1.5-68.1.6-70.1 2.4-2.9 2.6-2.8 7.6.2 10.4l2.3 2.2h155.3l2-2.6 2.1-2.7V165.6l-2.1-4.4c-1.5-2.9-3.7-5.4-6.7-7.3l-4.6-2.9h-19.4c-19.2 0-19.4 0-22.4-2.5-1.6-1.4-4.1-2.5-5.4-2.5-1.4 0-3.6 1.1-5 2.5zM313.5 263.4c11.3 5.2 16.6 17.3 12.9 29.8-.5 1.6-2.5 1.8-22.9 1.8-24.9 0-22.7.7-24-7.2-.9-5.1 1.8-14 5.8-18.5 7-7.9 18.6-10.4 28.2-5.9zM150.5 188.5c-2.4 2.3-2.5 3-2.5 14.5s.1 12.2 2.5 14.5c1.3 1.4 3.6 2.5 5 2.5s3.7-1.1 5-2.5c2.4-2.3 2.5-3 2.5-14.5s-.1-12.2-2.5-14.5c-1.3-1.4-3.6-2.5-5-2.5s-3.7 1.1-5 2.5z" />
    <path d="M321.5 191.1c-5.5 3.1-5.9 7.4-1 12.8 1.9 2.1 3.5 4.2 3.5 4.6 0 .4-1.6 2.5-3.5 4.6-3.8 4.2-4.1 5.5-2.4 9.3 2.5 5.3 9.6 4.9 14.8-.8l2.5-2.6 3.8 3.6c4.5 4.5 9.1 5 12 1.5 2.8-3.6 2.3-7.6-1.6-11.9l-3.4-3.7 3.4-3.8c5.6-6 4.3-12.4-2.8-14.2-2.2-.6-3.4 0-7 3.4l-4.4 4-3.5-3.4c-3.9-3.8-7.5-5-10.4-3.4zM446.1 200.6c-1.9 2.4-2.1 4-2.1 15.1 0 11.8.1 12.5 2.5 14.8 3.1 3.2 7.4 3.3 10.3.2 2-2.1 2.2-3.4 2.2-15.5 0-11.9-.2-13.4-2-15.2-3-3-8.3-2.7-10.9.6zM150.5 235.5c-2.4 2.3-2.5 3-2.5 14.5s.1 12.2 2.5 14.5c1.3 1.4 3.6 2.5 5 2.5s3.7-1.1 5-2.5c2.4-2.3 2.5-3 2.5-14.5s-.1-12.2-2.5-14.5c-1.3-1.4-3.6-2.5-5-2.5s-3.7 1.1-5 2.5zM446.5 251.5c-2.4 2.3-2.5 3-2.5 14.4 0 13.4.9 16.5 5.2 18 3.4 1.2 8.1-.6 9.2-3.4.3-.9.6-7.3.6-14.1 0-11.9-.1-12.6-2.5-14.9-1.3-1.4-3.6-2.5-5-2.5s-3.7 1.1-5 2.5zM446.1 303.6c-1.9 2.4-2.1 4-2.1 15.1 0 11.8.1 12.5 2.5 14.8 1.3 1.4 3.6 2.5 5 2.5s3.7-1.1 5-2.5c2.4-2.3 2.5-2.9 2.5-15.5 0-11.7-.2-13.2-2-15-3-3-8.3-2.7-10.9.6zM278.2 367.4c-3.3 3.1-3.7 4-3.7 8.4 0 4.7.4 5.4 13.5 22.7 17.2 22.7 18.5 24 25.3 25.5 3.6.7 21.6 1 56.3.8 46.4-.3 51.2-.5 54.1-2.1 6.1-3.3 8.1-11.4 4.4-17.2-1.1-1.7-7.9-10.8-15.3-20.4-8.8-11.5-14.5-18-16.8-19.2-3.3-1.8-6.8-1.9-58.7-1.9H282l-3.8 3.4zm122.3 26.2c6.1 8.1 11.2 15 11.3 15.5.5 1.3-93.4 1.2-95.8-.1-1.1-.6-6.9-7.6-12.9-15.5l-11-14.5h48.7l48.7.1 11 14.5zM214.1 387.6c-2.7 3.5-2.6 5.7.3 9.1 2.4 2.8 2.5 2.8 15.1 2.8s12.7 0 15.1-2.8c2.9-3.4 3-5.6.3-9.1-2-2.6-2.2-2.6-15.4-2.6s-13.4 0-15.4 2.6zM204.6 412.2c-2.1 3-2 7.1.3 9.9l1.9 2.4h26c24.2 0 26.1-.1 28.1-1.9 2.7-2.5 2.9-8.6.3-10.9-1.6-1.5-5.1-1.7-28.5-1.7-26.3 0-26.6 0-28.1 2.2z" />
  </svg>
);

export default ErgoIcon;
