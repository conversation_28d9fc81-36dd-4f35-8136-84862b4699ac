import { CSSProperties } from 'react';

const ErrorIcon = (props: { styles: CSSProperties }) => (
  <svg
    style={props.styles}
    version="1.0"
    viewBox="0 0 80 80"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M25.8 10.7c-1.4 1.6-6.5 9.1-11.2 16.8-8.8 14.3-10 18-7.6 22.6 2.3 4.2 5.8 4.9 25.5 4.9 14.9 0 19.2-.3 21.6-1.6 3.5-1.8 6.1-7 5.2-10.5-.9-3.3-18-31-20.5-33.2-3.3-2.8-9.9-2.3-13 1zM37 10c1 .5 6.2 7.9 11.4 16.3 10.8 17.2 11.7 20.4 7 24.8-2.5 2.3-3 2.4-21.2 2.7-16.4.2-19.1.1-22.2-1.5-3.8-2-5.6-5.6-4.7-9.4.8-3.1 18.2-30.8 20.5-32.5 2-1.6 6.6-1.8 9.2-.4z" />
    <path d="M29.4 15.2c-1.1 1.3-5.8 8.3-10.3 15.7-7.3 11.8-8.2 13.7-7.2 15.8C13 49 13 49 32.3 49c19.6 0 21.7-.4 21.7-3.9 0-1.7-16.7-29.1-18.9-30.9-2.1-1.7-3.5-1.5-5.7 1zm14 13.6c4.7 7.6 8.8 14.4 9.1 15.3 1.3 3.4-1.7 4-20.6 3.7-17.9-.3-18.4-.4-18.7-2.4-.3-2 15.7-29.1 18.1-30.6 2.1-1.3 3.8.7 12.1 14z" />
    <path d="M31 21c-1.8 1.1-2.6 11.1-1.1 13.9 1.4 2.5 4.6 2.8 5.5.5.3-.9.6-4.2.6-7.4 0-4.7-.4-6.1-1.8-6.9-1-.5-2.4-.6-3.2-.1zm3 8c0 5.6-.3 7-1.5 7S31 34.6 31 29s.3-7 1.5-7 1.5 1.4 1.5 7zM30.7 39.7c-.4.3-.7 1.7-.7 3 0 1.8.6 2.3 2.4 2.3 1.3 0 2.7-.7 3-1.6 1.1-2.8-2.7-5.7-4.7-3.7zm3.3 2.9c0 .8-.7 1.4-1.5 1.4-1.5 0-2.1-2.1-.9-3.3 1-.9 2.4.2 2.4 1.9zM51.7 9.8c-2.2 2.4-2.1 4.5.1 6.5 2.3 2.1 4.6 2.2 6.6.1 3-3 .9-8.4-3.3-8.4-1 0-2.5.8-3.4 1.8zm6.1.4c.7.7 1.2 1.8 1.2 2.4 0 1.4-2.7 4.4-4 4.4-1.1 0-4-2.9-4-4 0-1.3 3-4 4.4-4 .6 0 1.7.5 2.4 1.2zM6.7 21.2c-2.3 1.3-2.2 5.5.3 6.8 2.8 1.5 6.5-2.2 5.1-4.9-1.3-2.4-3.3-3.1-5.4-1.9zm4.1 3.1c.4 2-2.5 3.4-4 1.9s-.1-4.4 1.9-4c1 .2 1.9 1.1 2.1 2.1zM4 55c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1z" />
  </svg>
);

export default ErrorIcon;
