import { CSSProperties } from 'react';

const ErgoIcon = (props: { styles: CSSProperties }) => (
  <svg
    style={props.styles}
    version="1.0"
    viewBox="0 0 512 512"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M386.8 33c-2.7 2.8-3 3.6-2.5 7.3.7 5 4.4 8.7 8.8 8.7 7.7 0 12.4-6.5 9.5-13.3-2.7-6.6-10.6-7.9-15.8-2.7zM77.5 40c-9.4 1.9-17.6 7.3-22.4 14.8-9.3 14.6-6.9 33 5.8 44.4 7.4 6.7 14.4 9.1 24.6 8.6 13.3-.7 23.8-7.8 29.4-19.7 2.2-4.8 2.6-6.9 2.6-14.6 0-7.9-.4-9.7-2.8-14.3-5.7-10.8-13.9-17.1-25.1-19.1-6.6-1.2-6.5-1.2-12.1-.1zM90 60.8c10.3 5.1 10.8 20.6.8 25.8-4.5 2.4-11.2 2.2-14.6-.3-5.1-3.7-7.4-8.3-6.9-13.4C70.3 62.1 80.6 56 90 60.8zM236.5 50.1c-31.5 3-67 15.5-93 32.6-54.9 36.3-88.3 92.9-93.7 158.8-4.7 57.9 18.2 118.9 60.3 160.7 31.8 31.5 70.9 51.2 116.4 58.4 14.8 2.4 43.8 2.4 59.1 0 83.2-12.8 149.7-72.5 170.4-153 4.8-18.8 6.4-31.2 6.4-51.6 0-20.4-1.6-32.8-6.4-51.6-25.2-98-118.9-163.9-219.5-154.3zm39.3 20c79.4 8.6 145.3 67.4 162.6 145 16.8 75.9-14.1 153-78.9 196.4-36 24.2-80.5 35.1-123.3 30.4-46.1-5-90.1-27.8-120.3-62.3-34.7-39.5-51.4-91.9-45.8-143.4 8.7-80.3 68.4-146.4 147.5-163.1 18.6-4 39.5-5.1 58.2-3z" />
    <path d="M244 99.1c-20.7 1.4-38.1 6.3-58.5 16.4-22 10.9-41.1 26.9-56.5 47.5-7.8 10.4-18.4 30.9-22.5 43.3-10.3 31.2-10.3 68.2 0 99.4 3.9 11.8 14.7 32.8 21.9 42.5 33.5 45 83.1 68.1 138.6 64.7 77.4-4.8 141.2-68.5 145.8-145.6 2.8-46.7-11.9-87.2-43.8-120.7s-77.2-50.7-125-47.5zm2 29.4c0 10.7.8 9.7-9.5 11-8.6 1.2-21.6 5.4-32.1 10.5-34.9 16.9-60 51.3-65.3 89.4-1.5 10.6-1.4 18.2.4 21.5 1.9 3.7 6.6 5.7 10.7 4.5 5.3-1.6 6.9-4.8 7.8-16.8 1.1-13.7 3.9-23.6 10-36.1 9.4-19.3 25.2-35.1 44.5-44.5 12.4-6.1 22.4-8.9 36-10 11.4-.9 14.9-2.3 16.5-6.6.6-1.4 1-9.3 1-17.5V119h3.6c5.7 0 22.5 3.7 32.1 7.1 18.7 6.6 34.4 16.3 49 30.2 21.2 20.3 34.2 43.9 40.5 73 3 14.5 3 38.9 0 53.4-10.4 48.6-43.8 87-89.5 103.2-9.6 3.4-26.4 7.1-32.1 7.1H266v-9.5c0-10.7-.8-9.7 9.5-11 8.6-1.2 21.6-5.4 32.1-10.5 29.5-14.3 52.7-41.7 61.8-73.1 4.4-15.5 5.9-32.5 3.1-37.8-1.9-3.7-6.6-5.7-10.7-4.5-5.3 1.6-6.9 4.8-7.8 16.8-1.1 13.8-3.9 23.7-10 36.1-9.6 19.4-25.1 34.9-44.5 44.5-12.4 6.1-22.3 8.9-36 10-11.4.9-14.9 2.3-16.5 6.6-.6 1.4-1 9.3-1 17.5V393h-3.6c-5.6 0-22.4-3.7-32.1-7.1-10.4-3.6-25.2-11.3-34.3-17.7-9.3-6.6-23.2-20.3-30.5-30.1-11.7-15.5-20.3-34.8-24.7-55.4-3-14.5-3-38.9 0-53.4 10.4-48.6 43.7-87 89.5-103.2 10.1-3.6 25.2-6.9 31.5-7l4.2-.1v9.5z" />
    <path d="M304 196.2c-4.9 1.9-9 5.6-39 35.4l-33.5 33.3-9.5-9.3c-7.7-7.5-10.6-9.6-14.9-11.1-7.6-2.5-18.3-1.7-24.4 1.8-9.5 5.6-15 14.9-15 25.7-.1 11.7 1 13.3 32.1 44.2 15.7 15.6 28.7 27.8 30.1 28.1 1.3.3 3.8 0 5.5-.7 1.7-.8 26.1-24.5 54.2-52.7 56.7-56.9 54.9-54.8 54.9-67.9 0-20.6-20.8-34.3-40.5-26.8zm18.1 20.7c2.2 2.2 2.9 3.9 2.9 6.7 0 3.7-1.5 5.3-46.7 50.6L231.5 321l-20.8-20.7c-11.5-11.5-21.5-22-22.3-23.5-2.1-4.1-1.7-7.4 1.5-11 2.4-2.7 3.6-3.3 7-3.3 3.8 0 4.7.7 16.8 12.6 7.1 7 13.8 13 15.1 13.4 1.3.4 3.5.4 5 0 1.7-.4 15.8-13.7 39.7-37.6l37.1-36.9h4.3c3.4 0 4.9.6 7.2 2.9zM458.1 119.3c-9.6 5.5-4 20.6 6.7 18.2 5.7-1.2 9.1-7.5 6.8-12.8-.7-1.8-2-3.8-2.9-4.5-2.3-2-7.9-2.4-10.6-.9zM43.2 130.5c-1.6 1.4-3.2 3.7-3.5 5.2-1.2 6.5 5.1 13.2 11.3 11.8 3.6-.8 8-5.8 8-9 0-5.8-4.7-10.5-10.4-10.5-1.3 0-3.8 1.1-5.4 2.5zM45 375.3c-3.8 1.8-6.3 7.7-5 11.3 2.4 6.2 7.5 8.7 13.1 6.4 7.2-3 7.7-14 .8-17.6-3.4-1.7-5.6-1.8-8.9-.1zM458.1 375.3c-9.6 5.5-4 20.6 6.7 18.2 5.7-1.2 9.1-7.5 6.8-12.8-.7-1.8-2-3.8-2.9-4.5-2.3-2-7.9-2.4-10.6-.9zM406.2 424.5c-10.8 3-18.7 11.1-21.2 21.7-5.8 24.2 19.4 43.9 41.9 32.7 18.5-9.3 21.6-34.9 5.7-48.5-6.8-5.9-17.7-8.3-26.4-5.9zm14.6 22c2.6 3 3 9 .7 12.1-1.9 2.8-6.9 4.8-10.1 4-4.2-1.1-7.4-5-7.4-9.1 0-4.6 1.3-7.1 4.8-9 3.7-2.1 9.2-1.2 12 2zM101.9 455.8c-5.2 4.7-3.9 13 2.4 15.7 4.3 1.8 8.2.9 11.2-2.7 3.5-4.2 3.4-9.1-.4-12.9-4-3.9-8.9-4-13.2-.1z" />
  </svg>
);

export default ErgoIcon;
