import FillAmounts from '@/components/select-tokens/FillAmounts';
import TokenAmountInput from '@/components/token-amount-input/TokenAmountInput';
import Addresses from '@/pages/wallet-page/dapps/apps/air-drop/Addresses';
import React from 'react';
import SelectTokens from '@/components/select-tokens/SelectTokens';
import { AssetInfo, DAppPropsType, TokenAmount } from '@/types/dapps';
import { Button, Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import * as wasm from 'ergo-lib-wasm-browser';

const TX_FEE = 2000000n;
const IMPL_FEE = 5000000n;

const AirDrop = (props: DAppPropsType) => {
  const [addresses, setAddresses] = useState<Array<string>>(['']);
  const [ergAmount, setErgAmount] = useState(0n);
  const [totalErg, setTotalErg] = useState(0n);
  const [amounts, setAmounts] = useState<TokenAmount>({});
  const [tokens, setTokens] = useState<Array<AssetInfo>>([]);
  const [loaded, setLoaded] = useState(false);
  const [selectedTokenIds, setSelectedTokenIds] = useState<Array<string>>([]);
  const [addressError, setAddressError] = useState(false);
  const [acting, setActing] = useState(false);

  useEffect(() => {
    if (!loaded) {
      props.getAssets().then((tokens) => {
        props.getTokenAmount().then((res) => {
          setLoaded(true);
          setTotalErg(res - TX_FEE - IMPL_FEE);
        });
        setTokens(tokens);
      });
    }
  });

  const divisor = BigInt(Math.max(addresses.length, 1));
  const ergError = ergAmount < totalErg && ergAmount < BigInt(wasm.BoxValue.SAFE_USER_MIN().as_i64().to_str());
  const airdrop = async () => {
    if(!acting && !ergError && !addressError){
      setActing(true);
      const address = await props.getDefaultAddress();
      const height = await props.chain.getNetwork().getHeight();
      const airdrops = BigInt(addresses.length);
      const selectedTokens = Object.entries(amounts).map((item) => ({
        id: item[0],
        amount: item[1].amount * airdrops,
      }));
      const coveringBox = await props.getCoveringForErgAndToken(ergAmount * airdrops + TX_FEE + IMPL_FEE, selectedTokens)
      if (coveringBox.covered){
        
      }else{

      }
    }
  }
  return (
    <Stack spacing={2}>
      <Addresses
        addresses={addresses}
        setAddresses={setAddresses}
        setHasError={setAddressError}
      />
      <TokenAmountInput
        network_type={props.chain.label}
        amount={ergAmount}
        setAmount={(newAmount) => setErgAmount(newAmount)}
        total={totalErg / BigInt(Math.max(addresses.length, 1))}
        tokenId="erg"
        availableLabel="allowed each"
      />
      {tokens.length > 0 ? (
        <React.Fragment>
          <SelectTokens
            amounts={amounts}
            setAmounts={setAmounts}
            tokenIds={selectedTokenIds}
            setTokenIds={setSelectedTokenIds}
            getAssets={props.getAssets}
            chain={props.chain}
          />
          <FillAmounts
            amounts={amounts}
            setAmounts={setAmounts}
            tokenIds={selectedTokenIds}
            chain={props.chain}
            totalCalculator={(amount) => amount / divisor}
            availableLabel="allowed each"
          />
        </React.Fragment>
      ) : undefined}
      <Button onClick={() => null} disabled={addressError || ergError}>Air Drop</Button>
    </Stack>
  );
};

export default AirDrop;
