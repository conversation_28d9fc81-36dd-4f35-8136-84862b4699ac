import { TokenInfo, AbstractNetwork, BalanceInfo } from '@minotaur-ergo/types';
import { serialize } from '@/action/box';
import { AddressDbAction, BoxDbAction } from '@/action/db';
import ergoExplorerClientFactory, { V1 } from '@rosen-clients/ergo-explorer';
import * as wasm from 'ergo-lib-wasm-browser';
import Address from '@/db/entities/Address';
import JSONBigInt from 'json-bigint';
import { JsonBI } from '../json';

const getBoxId = (box: { boxId: string } | { id: string }) => {
  if (Object.prototype.hasOwnProperty.call(box, 'boxId'))
    return (box as { boxId: string }).boxId;
  return (box as { id: string }).id;
};

class ErgoExplorerNetwork extends AbstractNetwork {
  private readonly client;
  private static MAX_ALLOWED_TX_PER_PAGE = 100;

  constructor(url: string) {
    super();
    this.client = ergoExplorerClientFactory(url);
  }

  getHeight = async (): Promise<number> => {
    const info = await this.client.v1.getApiV1Info();
    return info.height;
  };

  getAddressTransactionCount = async (address: string): Promise<number> => {
    const data = await this.client.v1.getApiV1AddressesP1Transactions(address, {
      limit: 1,
    });
    return data.total;
  };

  getContext = async (): Promise<wasm.ErgoStateContext> => {
    const headers = (
      await this.client.v1.getApiV1BlocksHeaders({
        offset: 0,
        limit: 10,
      })
    ).items;
    if (headers) {
      const blockHeaders = wasm.BlockHeaders.from_json(
        headers.map((item) => JsonBI.stringify(item)),
      );
      const pre_header = wasm.PreHeader.from_block_header(blockHeaders.get(0));
      return new wasm.ErgoStateContext(pre_header, blockHeaders);
    }
    throw Error('Unknown error occurred');
  };

  sendTx = async (tx: wasm.Transaction): Promise<{ txId: string }> => {
    const res = await this.client.v1.postApiV1MempoolTransactionsSubmit(
      tx.to_json() as never,
    );
    return { txId: res.id };
  };

  getAddressInfo = async (address: string): Promise<BalanceInfo> => {
    const res =
      await this.client.v1.getApiV1AddressesP1BalanceConfirmed(address);
    return {
      nanoErgs: res.nanoErgs,
      tokens: res.tokens
        ? res.tokens.map((item) => ({ id: item.tokenId, amount: item.amount }))
        : [],
    };
  };

  getAssetDetails = async (assetId: string): Promise<TokenInfo> => {
    const tokenInfo = await this.client.v1.getApiV1TokensP1(assetId);
    const boxInfo = await this.client.v1.getApiV1BoxesP1(tokenInfo.boxId);
    return {
      name: tokenInfo.name,
      boxId: tokenInfo.boxId,
      id: tokenInfo.id,
      height: boxInfo.settlementHeight,
      decimals: tokenInfo.decimals,
      description: tokenInfo.description,
      emissionAmount: tokenInfo.emissionAmount,
      txId: boxInfo.transactionId,
    };
  };

  getBoxById = async (boxId: string): Promise<wasm.ErgoBox | undefined> => {
    const boxInfo = await this.client.v1.getApiV1BoxesP1(boxId);
    if (boxInfo !== undefined) {
      return wasm.ErgoBox.from_json(JsonBI.stringify(boxInfo));
    }
  };

  protected processTransactionInput = async (
    tx: V1.TransactionInfo | V1.TransactionInfo1,
    address: Address,
  ) => {
    for (const input of tx.inputs ?? []) {
      if (input.address === address.address) {
        await BoxDbAction.getInstance().spendBox(getBoxId(input), {
          height: tx.inclusionHeight,
          timestamp: parseInt(tx.timestamp.toString()),
          tx: tx.id,
          index: input.index,
        });
      }
    }
  };

  protected processTransactionOutput = async (
    tx: V1.TransactionInfo | V1.TransactionInfo1,
    address: Address,
  ) => {
    for (const output of tx.outputs ?? []) {
      if (output.address === address.address) {
        await BoxDbAction.getInstance().insertOrUpdateBox(
          {
            address: output.address,
            boxId: getBoxId(output),
            create: {
              index: output.index,
              tx: tx.id,
              height: tx.inclusionHeight,
              timestamp: parseInt(tx.timestamp.toString()),
            },
            serialized: serialize(
              wasm.ErgoBox.from_json(JsonBI.stringify(output)),
            ),
          },
          address,
        );
      }
    }
  };

  syncBoxes = async (addressStr: string): Promise<boolean> => {
    try {
      const address =
        await AddressDbAction.getInstance().getAddressByAddressString(
          addressStr,
        );
      if (address === null) return false;
      const height = await this.getHeight();
      let addressHeight = address.process_height;
      let toHeight = height;
      const proceedToHeight = async (proceedHeight: number) => {
        await AddressDbAction.getInstance().updateAddressHeight(
          address.id,
          proceedHeight,
        );
        addressHeight = proceedHeight;
        toHeight = height;
      };
      while (addressHeight < height) {
        let chunk = await this.client.v1.getApiV1AddressesP1Transactions(
          address.address,
          {
            limit: 1,
            offset: 0,
            fromHeight: addressHeight,
            toHeight: toHeight,
          },
        );
        if (chunk.total > ErgoExplorerNetwork.MAX_ALLOWED_TX_PER_PAGE) {
          if (toHeight > addressHeight + 1) {
            toHeight = Math.floor((toHeight + addressHeight) / 2);
          } else {
            const header = await this.client.v1.getApiV1BlocksHeaders({
              offset: addressHeight,
              limit: 1,
              sortBy: 'height',
              sortDirection: 'asc',
            });
            if (header.items === undefined) return false;
            const block = await this.client.v1.getApiV1BlocksP1(
              header.items[0].id,
            );
            for (const tx of block.block.blockTransactions ?? []) {
              await this.processTransactionOutput(tx, address);
            }
            for (const tx of block.block.blockTransactions ?? []) {
              await this.processTransactionInput(tx, address);
            }
            await proceedToHeight(toHeight);
          }
        } else {
          if (chunk.total > 1) {
            chunk = await this.client.v1.getApiV1AddressesP1Transactions(
              address.address,
              {
                limit: ErgoExplorerNetwork.MAX_ALLOWED_TX_PER_PAGE,
                offset: 0,
                fromHeight: addressHeight,
                toHeight: toHeight,
              },
            );
          }
          for (const tx of chunk.items ?? []) {
            await this.processTransactionOutput(tx, address);
          }
          for (const tx of chunk.items ?? []) {
            await this.processTransactionInput(tx, address);
          }
          await proceedToHeight(toHeight);
        }
      }
    } catch (e) {
      console.error(e);
      return false;
    }
    return true;
  };

  getUnspentBoxByTokenId = async (
    tokenId: string,
    offset: number,
    limit: number,
  ): Promise<Array<wasm.ErgoBox>> => {
    const boxes = await this.client.v1.getApiV1BoxesUnspentBytokenidP1(
      tokenId,
      { offset, limit },
    );
    if (boxes.items !== undefined) {
      return boxes.items.map((item) =>
        wasm.ErgoBox.from_json(JsonBI.stringify(item)),
      );
    }
    return [];
  };

  trackMempool = async (box: wasm.ErgoBox): Promise<wasm.ErgoBox> => {
    return box;
  };

  getTransaction = async (
    txId: string,
  ): Promise<{
    tx?: wasm.Transaction;
    date: string;
    boxes: Array<wasm.ErgoBox>;
  }> => {
    try {
      const res = await this.client.v1.getApiV1TransactionsP1(txId);
      if (res === undefined) return { date: '', boxes: [] };
      const boxes = (res.inputs || []).map((box) => {
        const boxJson = {
          creationHeight: box.outputCreatedAt,
          transactionId: box.outputTransactionId,
          boxId: box.boxId,
          value: box.value,
          index: box.outputIndex,
          ergoTree: box.ergoTree,
          assets: box.assets,
          additionalRegisters: box.additionalRegisters,
        };
        return wasm.ErgoBox.from_json(JsonBI.stringify(boxJson));
      });
      const txJson = {
        id: res.id,
        inputs: (res.inputs ?? []).map((item) => ({
          boxId: item.boxId,
          spendingProof: {
            proofBytes: item.spendingProof ? item.spendingProof : '',
            extension: {},
          },
        })),
        dataInputs: res.dataInputs,
        outputs: res.outputs,
      };
      const date = new Date(parseInt(res.timestamp.toString()));
      return {
        tx: wasm.Transaction.from_json(JSONBigInt.stringify(txJson)),
        date: date.toDateString() + ', ' + date.toLocaleTimeString(),
        boxes: boxes,
      };
    } catch (e) {
      console.error(e);
      return { date: '', boxes: [] };
    }
  };
}

export default ErgoExplorerNetwork;
