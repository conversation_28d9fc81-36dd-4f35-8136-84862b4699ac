export const MAX_CHUNK_SIZE = 2800;

export const QRCODE_SIZE_DEFAULT = 1000;
export const QRCODE_MINIMUM_CHUNK_SIZE = 200;
export const QRCODE_CHUNK_STEP = 100;

export const CONFIRMATION_HEIGHT = 720;

export const MAX_BLOCK_LENGTH = 100;

export const FEE = 1100000n;

export const MIN_BOX_VALUE = 1000000n;

export const REFRESH_INTERVAL = 2 * 60 * 1000;
export const PRICE_REFRESH_INTERVAL = 30 * 60 * 1000;

export const TX_CHUNK_SIZE = 1024;

export const COMMIT_ACTION = 'commit';
export const SIGN_ACTION = 'sign';
export const PUBLISH_ACTION = 'publish';

export const ADDRESS_PLACE_HOLDER = '#P2PK_ADDRESS#';
