PODS:
  - Capacitor (5.7.8):
    - Capacitor<PERSON>ordova
  - Capacitor<PERSON>pp (5.0.8):
    - Capacitor
  - CapacitorBrowser (5.2.1):
    - Capacitor
  - CapacitorCamera (5.0.10):
    - Capacitor
  - CapacitorClipboard (5.0.8):
    - Capacitor
  - CapacitorCommunitySqlite (5.6.1):
    - Capacitor
    - SQLCipher
    - ZIPFoundation
  - CapacitorCordova (5.7.8)
  - CapacitorFilesystem (5.2.2):
    - Capacitor
  - CapacitorMlkitBarcodeScanning (5.4.0):
    - Capacitor
    - GoogleMLKit/BarcodeScanning (= 4.0.0)
  - CapacitorPluginSafeArea (2.0.6):
    - Capacitor
  - CapacitorStatusBar (5.0.8):
    - Capacitor
  - CapacitorToast (5.0.8):
    - Capacitor
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (4.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 3.0.0)
  - GoogleMLKit/MLKitCore (4.0.0):
    - MLKitCommon (~> 9.0.0)
  - GoogleToolboxForMac/DebugUtils (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - GoogleToolboxForMac/Defines (2.3.2)
  - GoogleToolboxForMac/Logger (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSData+zlib (2.3.2)":
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSDictionary+URLArguments (2.3.2)":
    - GoogleToolboxForMac/DebugUtils (= 2.3.2)
    - GoogleToolboxForMac/Defines (= 2.3.2)
    - "GoogleToolboxForMac/NSString+URLArguments (= 2.3.2)"
  - "GoogleToolboxForMac/NSString+URLArguments (2.3.2)"
  - GoogleUtilities/Environment (7.11.6):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.6):
    - GoogleUtilities/Environment
  - GoogleUtilities/UserDefaults (7.11.6):
    - GoogleUtilities/Logger
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (2.3.0)
  - MLImage (1.0.0-beta4)
  - MLKitBarcodeScanning (3.0.0):
    - MLKitCommon (~> 9.0)
    - MLKitVision (~> 5.0)
  - MLKitCommon (9.0.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - "GoogleToolboxForMac/NSDictionary+URLArguments (~> 2.1)"
    - GoogleUtilities/UserDefaults (~> 7.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - MLKitVision (5.0.0):
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
    - MLImage (= 1.0.0-beta4)
    - MLKitCommon (~> 9.0)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.3.1)
  - SQLCipher (4.5.4):
    - SQLCipher/standard (= 4.5.4)
  - SQLCipher/common (4.5.4)
  - SQLCipher/standard (4.5.4):
    - SQLCipher/common
  - ZIPFoundation (0.9.16)

DEPENDENCIES:
  - "Capacitor (from `../../../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../../../node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../../../node_modules/@capacitor/browser`)"
  - "CapacitorCamera (from `../../../../node_modules/@capacitor/camera`)"
  - "CapacitorClipboard (from `../../../../node_modules/@capacitor/clipboard`)"
  - "CapacitorCommunitySqlite (from `../../../../node_modules/@capacitor-community/sqlite`)"
  - "CapacitorCordova (from `../../../../node_modules/@capacitor/ios`)"
  - "CapacitorFilesystem (from `../../../../node_modules/@capacitor/filesystem`)"
  - "CapacitorMlkitBarcodeScanning (from `../../../../node_modules/@capacitor-mlkit/barcode-scanning`)"
  - CapacitorPluginSafeArea (from `../../../../node_modules/capacitor-plugin-safe-area`)
  - "CapacitorStatusBar (from `../../../../node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../../../node_modules/@capacitor/toast`)"
  - GoogleMLKit/BarcodeScanning

SPEC REPOS:
  trunk:
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - SQLCipher
    - ZIPFoundation

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../../../node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../../../node_modules/@capacitor/browser"
  CapacitorCamera:
    :path: "../../../../node_modules/@capacitor/camera"
  CapacitorClipboard:
    :path: "../../../../node_modules/@capacitor/clipboard"
  CapacitorCommunitySqlite:
    :path: "../../../../node_modules/@capacitor-community/sqlite"
  CapacitorCordova:
    :path: "../../../../node_modules/@capacitor/ios"
  CapacitorFilesystem:
    :path: "../../../../node_modules/@capacitor/filesystem"
  CapacitorMlkitBarcodeScanning:
    :path: "../../../../node_modules/@capacitor-mlkit/barcode-scanning"
  CapacitorPluginSafeArea:
    :path: "../../../../node_modules/capacitor-plugin-safe-area"
  CapacitorStatusBar:
    :path: "../../../../node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../../../node_modules/@capacitor/toast"

SPEC CHECKSUMS:
  Capacitor: 747aadb4fa786f460d8cd9d0557cb81440a13747
  CapacitorApp: 102e553c3b1fdc255969c39aa19b529675690ef4
  CapacitorBrowser: bac3132be6506411704149f9f2099843ffe470a0
  CapacitorCamera: d333715364b5d85422c627aae6db87ef6161352c
  CapacitorClipboard: 03628c89fe70d06d36288ee4eff7e205a6e81e53
  CapacitorCommunitySqlite: 47474c9717db7d998eeba2c3147e1c055a726d5f
  CapacitorCordova: 31ab98dca2ddcee051027a1afe7e8c85c82f7297
  CapacitorFilesystem: 202b158a878462ab4f401d0eb4d9644c67927333
  CapacitorMlkitBarcodeScanning: 8fb81cbef3c6ffe0c0e2dbd15ed6dca889a5a062
  CapacitorPluginSafeArea: d55e4d4e70c98f611240f11dd80561438d0d70cb
  CapacitorStatusBar: 51af549cfcf35aac3ab072b062967ae5971ee485
  CapacitorToast: 5967ced673b9fb9b7d7b8520c648dbd8f5064b15
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleMLKit: 2bd0dc6253c4d4f227aad460f69215a504b2980e
  GoogleToolboxForMac: 8bef7c7c5cf7291c687cf5354f39f9db6399ad34
  GoogleUtilities: 202e7a9f5128accd11160fb9c19612de1911aa19
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  MLImage: 7bb7c4264164ade9bf64f679b40fb29c8f33ee9b
  MLKitBarcodeScanning: 04e264482c5f3810cb89ebc134ef6b61e67db505
  MLKitCommon: c1b791c3e667091918d91bda4bba69a91011e390
  MLKitVision: 8baa5f46ee3352614169b85250574fde38c36f49
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  SQLCipher: 905b145f65f349f26da9e60a19901ad24adcd381
  ZIPFoundation: d170fa8e270b2a32bef9dcdcabff5b8f1a5deced

PODFILE CHECKSUM: b0f792207ce8b0ecb3967796322be793954734f3

COCOAPODS: 1.15.2
