require_relative '../../../../node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '13.0'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../../../node_modules/@capacitor/ios'
  pod 'CapacitorCommunitySqlite', :path => '../../../../node_modules/@capacitor-community/sqlite'
  pod 'CapacitorMlkitBarcodeScanning', :path => '../../../../node_modules/@capacitor-mlkit/barcode-scanning'
  pod 'CapacitorApp', :path => '../../../../node_modules/@capacitor/app'
  pod 'CapacitorBrowser', :path => '../../../../node_modules/@capacitor/browser'
  pod 'CapacitorCamera', :path => '../../../../node_modules/@capacitor/camera'
  pod 'CapacitorClipboard', :path => '../../../../node_modules/@capacitor/clipboard'
  pod 'CapacitorFilesystem', :path => '../../../../node_modules/@capacitor/filesystem'
  pod 'CapacitorStatusBar', :path => '../../../../node_modules/@capacitor/status-bar'
  pod 'CapacitorToast', :path => '../../../../node_modules/@capacitor/toast'
  pod 'CapacitorPluginSafeArea', :path => '../../../../node_modules/capacitor-plugin-safe-area'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
  pod 'GoogleMLKit/BarcodeScanning'
end

post_install do |installer|
  assertDeploymentTarget(installer)
end
