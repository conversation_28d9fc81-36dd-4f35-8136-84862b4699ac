{"name": "minotaur-wallet", "private": true, "version": "2.5.0", "type": "module", "scripts": {"postinstall": "patch-package && npm run copyIcons", "dev": "vite --host", "clean": "rimraf ./dist", "build": "tsc && vite build", "type-check": "tsc", "lint": "npm run prettify && eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preprocess": "npx madge --circular --extensions ts --extensions tsx .", "prettify": "prettier --ignore-unknown --write .", "preview": "vite preview", "sync:electron": "npm run clean && npm run build && cap sync electron && npx cap update electron", "sync": "npm run clean && npm run build && npx cap sync && npx cap update && cap sync electron && npx cap update electron", "test": "echo 'no test'", "coverage": "npm run test -- --coverage", "copyIcons": "node bin/copy-icons.js"}, "dependencies": {"@capacitor-community/electron": "^5.0.1", "@capacitor-community/sqlite": "5.6.1", "@capacitor-mlkit/barcode-scanning": "^5.4.0", "@capacitor/android": "^5.6.0", "@capacitor/app": "^5.0.7", "@capacitor/browser": "^5.2.0", "@capacitor/camera": "^5.0.9", "@capacitor/cli": "^5.6.0", "@capacitor/clipboard": "^5.0.7", "@capacitor/core": "^5.6.0", "@capacitor/filesystem": "^5.2.1", "@capacitor/ios": "^5.6.0", "@capacitor/status-bar": "^5.0.7", "@capacitor/toast": "^5.0.7", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@minotaur-ergo/icons": "^1.1.0", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@noble/curves": "^1.9.1", "@reduxjs/toolkit": "^2.2.5", "@rosen-clients/ergo-explorer": "^1.1.1", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.1", "bip32": "^4.0.0", "bip39": "^3.1.0", "blakejs": "^1.2.1", "bs58": "^6.0.0", "buffer": "^6.0.3", "buffer-polyfill": "^6.0.3", "capacitor-plugin-safe-area": "^2.0.6", "clipboardy": "^4.0.0", "crypto-js": "^4.2.0", "ergo-lib-wasm-browser": "0.26.0", "generate-password": "^1.7.1", "is-electron": "^2.2.2", "json-bigint": "^1.0.0", "notistack": "^3.0.1", "patch-package": "^8.0.0", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-loading": "^2.0.3", "react-qr-code": "^2.0.15", "react-redux": "^9.1.2", "react-router-dom": "^6.24.0", "reflect-metadata": "^0.2.2", "tiny-secp256k1": "^2.2.3", "typeorm": "^0.3.20", "uuid": "^10.0.0", "websocket-ts": "^2.1.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/json-bigint": "^1.0.4", "@types/react": "^18.3.3", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.0", "@types/sql.js": "1.4.9", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.7", "madge": "^7.0.0", "prettier": "^3.3.2", "rimraf": "^5.0.7", "sql.js": "1.6.2", "typescript": "^5.5.2", "vite": "^5.3.1", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-wasm": "^3.3.0"}, "engines": {"node": ">=18.16.0"}}