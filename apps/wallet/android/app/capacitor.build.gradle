// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-sqlite')
    implementation project(':capacitor-mlkit-barcode-scanning')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-toast')
    implementation project(':capacitor-plugin-safe-area')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
