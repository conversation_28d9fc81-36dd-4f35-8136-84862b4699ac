// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN
include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../../../node_modules/@capacitor/android/capacitor')

include ':capacitor-community-sqlite'
project(':capacitor-community-sqlite').projectDir = new File('../../../node_modules/@capacitor-community/sqlite/android')

include ':capacitor-mlkit-barcode-scanning'
project(':capacitor-mlkit-barcode-scanning').projectDir = new File('../../../node_modules/@capacitor-mlkit/barcode-scanning/android')

include ':capacitor-app'
project(':capacitor-app').projectDir = new File('../../../node_modules/@capacitor/app/android')

include ':capacitor-browser'
project(':capacitor-browser').projectDir = new File('../../../node_modules/@capacitor/browser/android')

include ':capacitor-camera'
project(':capacitor-camera').projectDir = new File('../../../node_modules/@capacitor/camera/android')

include ':capacitor-clipboard'
project(':capacitor-clipboard').projectDir = new File('../../../node_modules/@capacitor/clipboard/android')

include ':capacitor-filesystem'
project(':capacitor-filesystem').projectDir = new File('../../../node_modules/@capacitor/filesystem/android')

include ':capacitor-status-bar'
project(':capacitor-status-bar').projectDir = new File('../../../node_modules/@capacitor/status-bar/android')

include ':capacitor-toast'
project(':capacitor-toast').projectDir = new File('../../../node_modules/@capacitor/toast/android')

include ':capacitor-plugin-safe-area'
project(':capacitor-plugin-safe-area').projectDir = new File('../../../node_modules/capacitor-plugin-safe-area/android')
