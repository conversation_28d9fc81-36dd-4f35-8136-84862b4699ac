apps/wallet/android/*
apps/wallet/ios/*
apps/wallet/electron/app/*
apps/wallet/electron/build/*
apps/wallet/electron/dist/*

# Copied from `.gitignore`. Please try to keep them in sync.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/apps/wallet/build
/apps/wallet/extension

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.idea/*
/dist
/extension
