name: Build for Android
on:
  push:
    tags:
      - 'v*.*.*'
      - 'android-v*.*.*'

env:
  CI: false

jobs:
  android:
    runs-on: ubuntu-latest
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.*

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Extract version from tag
        uses: damienaicheh/extract-version-from-tag-action@v1.1.0

      - name: Update package.json files
        shell: bash
        run: node cli/update_version.js '${{ env.MAJOR }}.${{ env.MINOR }}.${{ env.PATCH }}${{ env.PRE_RELEASE }}'

      - name: Install app dependencies
        shell: bash
        run: npm install

      - name: Build icons
        shell: bash
        run: ./cli/build.sh

      - name: Copy icons to public dir
        shell: bash
        run: npm run copyIcons --prefix apps/wallet

      - name: Build Minotaur JS app and update capacitor
        shell: bash
        run: npm run sync --prefix apps/wallet/

      - name: Setup Android build environment
        uses: android-actions/setup-android@v3

      - name: Grant execute permission for gradlew
        run: chmod +x ./apps/wallet/android/gradlew

      - name: Update AndroidManifest.xml
        uses: damienaicheh/update-android-version-manifest-action@v1.0.0
        with:
          android-manifest-path: './apps/wallet/android/app/src/main/AndroidManifest.xml'
          version-code: ${{ env.NUMBER_OF_COMMITS }}
          version-name: '${{ env.MAJOR }}.${{ env.MINOR }}.${{ env.PATCH }}${{ env.PRE_RELEASE }}'
          print-file: true

      - name: Update gradle version for Android
        uses: damienaicheh/update-android-version-gradle-action@v1.0.0
        with:
          build-gradle-path: './apps/wallet/android/app/build.gradle'
          version-code: ${{ env.NUMBER_OF_COMMITS }}
          version-name: '${{ env.MAJOR }}.${{ env.MINOR }}.${{ env.PATCH }}${{ env.PRE_RELEASE }}'
          print-file: true

      - name: Build app bundle
        working-directory: apps/wallet/android
        run: ./gradlew clean app:assembleRelease

      - uses: r0adkll/sign-android-release@v1
        name: Sign app APK
        # ID used to access action output
        id: sign_app
        env:
          BUILD_TOOLS_VERSION: 34.0.0
        with:
          releaseDirectory: apps/wallet/android/app/build/outputs/apk/release
          signingKeyBase64: ${{ secrets.SIGNING_KEYSTORE }}
          alias: ${{ secrets.SIGNING_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.SIGNING_STORE_PASSWORD }}
          keyPassword: ${{ secrets.SIGNING_KEY_PASSWORD }}

      - name: move apk to root directory
        run: mv ${{steps.sign_app.outputs.signedReleaseFile}} minotaur-${{ env.MAJOR }}.${{ env.MINOR }}.${{ env.PATCH }}${{ env.PRE_RELEASE }}.apk

      - name: Upload Apk File to release
        uses: softprops/action-gh-release@v1
        with:
          files: minotaur*.apk
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
